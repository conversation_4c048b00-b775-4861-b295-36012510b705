{"version": 3, "file": "index.js", "names": ["throwError", "isNodePattern", "blit", "src", "x", "y", "srcx", "srcy", "srcw", "srch", "cb", "constructor", "call", "bitmap", "width", "height", "Math", "round", "max<PERSON><PERSON><PERSON>", "maxHeight", "baseImage", "scanQuiet", "sx", "sy", "idx", "xOffset", "yOffset", "dstIdx", "getPixelIndex", "r", "data", "g", "b", "a", "dst", "limit255"], "sources": ["../src/index.js"], "sourcesContent": ["import { throwError, isNodePattern } from \"@jimp/utils\";\n\nexport default () => ({\n  /**\n   * Blits a source image on to this image\n   * @param {Jimp} src the source Jimp instance\n   * @param {number} x the x position to blit the image\n   * @param {number} y the y position to blit the image\n   * @param {number} srcx (optional) the x position from which to crop the source image\n   * @param {number} srcy (optional) the y position from which to crop the source image\n   * @param {number} srcw (optional) the width to which to crop the source image\n   * @param {number} srch (optional) the height to which to crop the source image\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp} this for chaining of methods\n   */\n  blit(src, x, y, srcx, srcy, srcw, srch, cb) {\n    if (!(src instanceof this.constructor)) {\n      return throwError.call(this, \"The source must be a Jimp image\", cb);\n    }\n\n    if (typeof x !== \"number\" || typeof y !== \"number\") {\n      return throwError.call(this, \"x and y must be numbers\", cb);\n    }\n\n    if (typeof srcx === \"function\") {\n      cb = srcx;\n      srcx = 0;\n      srcy = 0;\n      srcw = src.bitmap.width;\n      srch = src.bitmap.height;\n    } else if (\n      typeof srcx === typeof srcy &&\n      typeof srcy === typeof srcw &&\n      typeof srcw === typeof srch\n    ) {\n      srcx = srcx || 0;\n      srcy = srcy || 0;\n      srcw = srcw || src.bitmap.width;\n      srch = srch || src.bitmap.height;\n    } else {\n      return throwError.call(\n        this,\n        \"srcx, srcy, srcw, srch must be numbers\",\n        cb\n      );\n    }\n\n    // round input\n    x = Math.round(x);\n    y = Math.round(y);\n\n    // round input\n    srcx = Math.round(srcx);\n    srcy = Math.round(srcy);\n    srcw = Math.round(srcw);\n    srch = Math.round(srch);\n\n    const maxWidth = this.bitmap.width;\n    const maxHeight = this.bitmap.height;\n    const baseImage = this;\n\n    src.scanQuiet(srcx, srcy, srcw, srch, function (sx, sy, idx) {\n      const xOffset = x + sx - srcx;\n      const yOffset = y + sy - srcy;\n\n      if (\n        xOffset >= 0 &&\n        yOffset >= 0 &&\n        maxWidth - xOffset > 0 &&\n        maxHeight - yOffset > 0\n      ) {\n        const dstIdx = baseImage.getPixelIndex(xOffset, yOffset);\n        const src = {\n          r: this.bitmap.data[idx],\n          g: this.bitmap.data[idx + 1],\n          b: this.bitmap.data[idx + 2],\n          a: this.bitmap.data[idx + 3],\n        };\n\n        const dst = {\n          r: baseImage.bitmap.data[dstIdx],\n          g: baseImage.bitmap.data[dstIdx + 1],\n          b: baseImage.bitmap.data[dstIdx + 2],\n          a: baseImage.bitmap.data[dstIdx + 3],\n        };\n\n        baseImage.bitmap.data[dstIdx] =\n          ((src.a * (src.r - dst.r) - dst.r + 255) >> 8) + dst.r;\n        baseImage.bitmap.data[dstIdx + 1] =\n          ((src.a * (src.g - dst.g) - dst.g + 255) >> 8) + dst.g;\n        baseImage.bitmap.data[dstIdx + 2] =\n          ((src.a * (src.b - dst.b) - dst.b + 255) >> 8) + dst.b;\n        baseImage.bitmap.data[dstIdx + 3] = this.constructor.limit255(\n          dst.a + src.a\n        );\n      }\n    });\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n});\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,aAAa,QAAQ,aAAa;AAEvD,gBAAe,OAAO;EACpB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,IAAI,CAACC,GAAG,EAAEC,CAAC,EAAEC,CAAC,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,EAAE,EAAE;IAC1C,IAAI,EAAEP,GAAG,YAAY,IAAI,CAACQ,WAAW,CAAC,EAAE;MACtC,OAAOX,UAAU,CAACY,IAAI,CAAC,IAAI,EAAE,iCAAiC,EAAEF,EAAE,CAAC;IACrE;IAEA,IAAI,OAAON,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE;MAClD,OAAOL,UAAU,CAACY,IAAI,CAAC,IAAI,EAAE,yBAAyB,EAAEF,EAAE,CAAC;IAC7D;IAEA,IAAI,OAAOJ,IAAI,KAAK,UAAU,EAAE;MAC9BI,EAAE,GAAGJ,IAAI;MACTA,IAAI,GAAG,CAAC;MACRC,IAAI,GAAG,CAAC;MACRC,IAAI,GAAGL,GAAG,CAACU,MAAM,CAACC,KAAK;MACvBL,IAAI,GAAGN,GAAG,CAACU,MAAM,CAACE,MAAM;IAC1B,CAAC,MAAM,IACL,OAAOT,IAAI,KAAK,OAAOC,IAAI,IAC3B,OAAOA,IAAI,KAAK,OAAOC,IAAI,IAC3B,OAAOA,IAAI,KAAK,OAAOC,IAAI,EAC3B;MACAH,IAAI,GAAGA,IAAI,IAAI,CAAC;MAChBC,IAAI,GAAGA,IAAI,IAAI,CAAC;MAChBC,IAAI,GAAGA,IAAI,IAAIL,GAAG,CAACU,MAAM,CAACC,KAAK;MAC/BL,IAAI,GAAGA,IAAI,IAAIN,GAAG,CAACU,MAAM,CAACE,MAAM;IAClC,CAAC,MAAM;MACL,OAAOf,UAAU,CAACY,IAAI,CACpB,IAAI,EACJ,wCAAwC,EACxCF,EAAE,CACH;IACH;;IAEA;IACAN,CAAC,GAAGY,IAAI,CAACC,KAAK,CAACb,CAAC,CAAC;IACjBC,CAAC,GAAGW,IAAI,CAACC,KAAK,CAACZ,CAAC,CAAC;;IAEjB;IACAC,IAAI,GAAGU,IAAI,CAACC,KAAK,CAACX,IAAI,CAAC;IACvBC,IAAI,GAAGS,IAAI,CAACC,KAAK,CAACV,IAAI,CAAC;IACvBC,IAAI,GAAGQ,IAAI,CAACC,KAAK,CAACT,IAAI,CAAC;IACvBC,IAAI,GAAGO,IAAI,CAACC,KAAK,CAACR,IAAI,CAAC;IAEvB,MAAMS,QAAQ,GAAG,IAAI,CAACL,MAAM,CAACC,KAAK;IAClC,MAAMK,SAAS,GAAG,IAAI,CAACN,MAAM,CAACE,MAAM;IACpC,MAAMK,SAAS,GAAG,IAAI;IAEtBjB,GAAG,CAACkB,SAAS,CAACf,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAE,UAAUa,EAAE,EAAEC,EAAE,EAAEC,GAAG,EAAE;MAC3D,MAAMC,OAAO,GAAGrB,CAAC,GAAGkB,EAAE,GAAGhB,IAAI;MAC7B,MAAMoB,OAAO,GAAGrB,CAAC,GAAGkB,EAAE,GAAGhB,IAAI;MAE7B,IACEkB,OAAO,IAAI,CAAC,IACZC,OAAO,IAAI,CAAC,IACZR,QAAQ,GAAGO,OAAO,GAAG,CAAC,IACtBN,SAAS,GAAGO,OAAO,GAAG,CAAC,EACvB;QACA,MAAMC,MAAM,GAAGP,SAAS,CAACQ,aAAa,CAACH,OAAO,EAAEC,OAAO,CAAC;QACxD,MAAMvB,GAAG,GAAG;UACV0B,CAAC,EAAE,IAAI,CAAChB,MAAM,CAACiB,IAAI,CAACN,GAAG,CAAC;UACxBO,CAAC,EAAE,IAAI,CAAClB,MAAM,CAACiB,IAAI,CAACN,GAAG,GAAG,CAAC,CAAC;UAC5BQ,CAAC,EAAE,IAAI,CAACnB,MAAM,CAACiB,IAAI,CAACN,GAAG,GAAG,CAAC,CAAC;UAC5BS,CAAC,EAAE,IAAI,CAACpB,MAAM,CAACiB,IAAI,CAACN,GAAG,GAAG,CAAC;QAC7B,CAAC;QAED,MAAMU,GAAG,GAAG;UACVL,CAAC,EAAET,SAAS,CAACP,MAAM,CAACiB,IAAI,CAACH,MAAM,CAAC;UAChCI,CAAC,EAAEX,SAAS,CAACP,MAAM,CAACiB,IAAI,CAACH,MAAM,GAAG,CAAC,CAAC;UACpCK,CAAC,EAAEZ,SAAS,CAACP,MAAM,CAACiB,IAAI,CAACH,MAAM,GAAG,CAAC,CAAC;UACpCM,CAAC,EAAEb,SAAS,CAACP,MAAM,CAACiB,IAAI,CAACH,MAAM,GAAG,CAAC;QACrC,CAAC;QAEDP,SAAS,CAACP,MAAM,CAACiB,IAAI,CAACH,MAAM,CAAC,GAC3B,CAAExB,GAAG,CAAC8B,CAAC,IAAI9B,GAAG,CAAC0B,CAAC,GAAGK,GAAG,CAACL,CAAC,CAAC,GAAGK,GAAG,CAACL,CAAC,GAAG,GAAG,IAAK,CAAC,IAAIK,GAAG,CAACL,CAAC;QACxDT,SAAS,CAACP,MAAM,CAACiB,IAAI,CAACH,MAAM,GAAG,CAAC,CAAC,GAC/B,CAAExB,GAAG,CAAC8B,CAAC,IAAI9B,GAAG,CAAC4B,CAAC,GAAGG,GAAG,CAACH,CAAC,CAAC,GAAGG,GAAG,CAACH,CAAC,GAAG,GAAG,IAAK,CAAC,IAAIG,GAAG,CAACH,CAAC;QACxDX,SAAS,CAACP,MAAM,CAACiB,IAAI,CAACH,MAAM,GAAG,CAAC,CAAC,GAC/B,CAAExB,GAAG,CAAC8B,CAAC,IAAI9B,GAAG,CAAC6B,CAAC,GAAGE,GAAG,CAACF,CAAC,CAAC,GAAGE,GAAG,CAACF,CAAC,GAAG,GAAG,IAAK,CAAC,IAAIE,GAAG,CAACF,CAAC;QACxDZ,SAAS,CAACP,MAAM,CAACiB,IAAI,CAACH,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAChB,WAAW,CAACwB,QAAQ,CAC3DD,GAAG,CAACD,CAAC,GAAG9B,GAAG,CAAC8B,CAAC,CACd;MACH;IACF,CAAC,CAAC;IAEF,IAAIhC,aAAa,CAACS,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb;AACF,CAAC,CAAC"}