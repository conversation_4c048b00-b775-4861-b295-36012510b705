{"version": 3, "file": "index.js", "names": ["configure", "configuration", "jimpInstance", "<PERSON><PERSON>", "jimpConfig", "has<PERSON><PERSON><PERSON>", "encoders", "decoders", "class", "constants", "addToConfig", "newConfig", "Object", "entries", "for<PERSON>ach", "key", "value", "addImageType", "typeModule", "type", "Array", "isArray", "mime", "addType", "mimeType", "addPlugin", "pluginModule", "plugin", "jimp<PERSON>v<PERSON><PERSON><PERSON>", "types", "plugins", "addJimpMethods", "addConstants"], "sources": ["../src/index.js"], "sourcesContent": ["import Jimp, {\n  addType,\n  addJimpMethods,\n  addConstants,\n  jimpEvChange,\n} from \"@jimp/core\";\n\nexport default function configure(configuration, jimpInstance = Jimp) {\n  const jimpConfig = {\n    hasAlpha: {},\n    encoders: {},\n    decoders: {},\n    class: {},\n    constants: {},\n  };\n\n  function addToConfig(newConfig) {\n    Object.entries(newConfig).forEach(([key, value]) => {\n      jimpConfig[key] = {\n        ...jimpConfig[key],\n        ...value,\n      };\n    });\n  }\n\n  function addImageType(typeModule) {\n    const type = typeModule();\n\n    if (Array.isArray(type.mime)) {\n      addType(...type.mime);\n    } else {\n      Object.entries(type.mime).forEach((mimeType) => addType(...mimeType));\n    }\n\n    delete type.mime;\n    addToConfig(type);\n  }\n\n  function addPlugin(pluginModule) {\n    const plugin = pluginModule(jimpEvChange) || {};\n    if (!plugin.class && !plugin.constants) {\n      // Default to class function\n      addToConfig({ class: plugin });\n    } else {\n      addToConfig(plugin);\n    }\n  }\n\n  if (configuration.types) {\n    configuration.types.forEach(addImageType);\n\n    jimpInstance.decoders = {\n      ...jimpInstance.decoders,\n      ...jimpConfig.decoders,\n    };\n    jimpInstance.encoders = {\n      ...jimpInstance.encoders,\n      ...jimpConfig.encoders,\n    };\n    jimpInstance.hasAlpha = {\n      ...jimpInstance.hasAlpha,\n      ...jimpConfig.hasAlpha,\n    };\n  }\n\n  if (configuration.plugins) {\n    configuration.plugins.forEach(addPlugin);\n  }\n\n  addJimpMethods(jimpConfig.class, jimpInstance);\n  addConstants(jimpConfig.constants, jimpInstance);\n\n  return Jimp;\n}\n"], "mappings": ";;;;;;AAAA;AAKoB;AAAA;AAEL,SAASA,SAAS,CAACC,aAAa,EAAuB;EAAA,IAArBC,YAAY,uEAAGC,aAAI;EAClE,MAAMC,UAAU,GAAG;IACjBC,QAAQ,EAAE,CAAC,CAAC;IACZC,QAAQ,EAAE,CAAC,CAAC;IACZC,QAAQ,EAAE,CAAC,CAAC;IACZC,KAAK,EAAE,CAAC,CAAC;IACTC,SAAS,EAAE,CAAC;EACd,CAAC;EAED,SAASC,WAAW,CAACC,SAAS,EAAE;IAC9BC,MAAM,CAACC,OAAO,CAACF,SAAS,CAAC,CAACG,OAAO,CAAC,QAAkB;MAAA,IAAjB,CAACC,GAAG,EAAEC,KAAK,CAAC;MAC7CZ,UAAU,CAACW,GAAG,CAAC,GAAG;QAChB,GAAGX,UAAU,CAACW,GAAG,CAAC;QAClB,GAAGC;MACL,CAAC;IACH,CAAC,CAAC;EACJ;EAEA,SAASC,YAAY,CAACC,UAAU,EAAE;IAChC,MAAMC,IAAI,GAAGD,UAAU,EAAE;IAEzB,IAAIE,KAAK,CAACC,OAAO,CAACF,IAAI,CAACG,IAAI,CAAC,EAAE;MAC5B,IAAAC,aAAO,EAAC,GAAGJ,IAAI,CAACG,IAAI,CAAC;IACvB,CAAC,MAAM;MACLV,MAAM,CAACC,OAAO,CAACM,IAAI,CAACG,IAAI,CAAC,CAACR,OAAO,CAAEU,QAAQ,IAAK,IAAAD,aAAO,EAAC,GAAGC,QAAQ,CAAC,CAAC;IACvE;IAEA,OAAOL,IAAI,CAACG,IAAI;IAChBZ,WAAW,CAACS,IAAI,CAAC;EACnB;EAEA,SAASM,SAAS,CAACC,YAAY,EAAE;IAC/B,MAAMC,MAAM,GAAGD,YAAY,CAACE,kBAAY,CAAC,IAAI,CAAC,CAAC;IAC/C,IAAI,CAACD,MAAM,CAACnB,KAAK,IAAI,CAACmB,MAAM,CAAClB,SAAS,EAAE;MACtC;MACAC,WAAW,CAAC;QAAEF,KAAK,EAAEmB;MAAO,CAAC,CAAC;IAChC,CAAC,MAAM;MACLjB,WAAW,CAACiB,MAAM,CAAC;IACrB;EACF;EAEA,IAAI1B,aAAa,CAAC4B,KAAK,EAAE;IACvB5B,aAAa,CAAC4B,KAAK,CAACf,OAAO,CAACG,YAAY,CAAC;IAEzCf,YAAY,CAACK,QAAQ,GAAG;MACtB,GAAGL,YAAY,CAACK,QAAQ;MACxB,GAAGH,UAAU,CAACG;IAChB,CAAC;IACDL,YAAY,CAACI,QAAQ,GAAG;MACtB,GAAGJ,YAAY,CAACI,QAAQ;MACxB,GAAGF,UAAU,CAACE;IAChB,CAAC;IACDJ,YAAY,CAACG,QAAQ,GAAG;MACtB,GAAGH,YAAY,CAACG,QAAQ;MACxB,GAAGD,UAAU,CAACC;IAChB,CAAC;EACH;EAEA,IAAIJ,aAAa,CAAC6B,OAAO,EAAE;IACzB7B,aAAa,CAAC6B,OAAO,CAAChB,OAAO,CAACW,SAAS,CAAC;EAC1C;EAEA,IAAAM,oBAAc,EAAC3B,UAAU,CAACI,KAAK,EAAEN,YAAY,CAAC;EAC9C,IAAA8B,kBAAY,EAAC5B,UAAU,CAACK,SAAS,EAAEP,YAAY,CAAC;EAEhD,OAAOC,aAAI;AACb;AAAC;AAAA"}