{"version": 3, "file": "index.js", "names": ["throwError", "isNodePattern", "pluginCrop", "event", "x", "y", "w", "h", "cb", "call", "Math", "round", "bitmap", "width", "start", "end", "data", "slice", "<PERSON><PERSON><PERSON>", "allocUnsafe", "offset", "scanQuiet", "idx", "readUInt32BE", "writeUInt32BE", "height", "class", "autocrop", "minPixelsPerSide", "leaveBorder", "tolerance", "cropOnlyFrames", "cropSymmetric", "ignoreSides", "north", "south", "east", "west", "args", "a", "len", "length", "config", "colorTarget", "getPixelColor", "rgba1", "constructor", "intToRGBA", "northPixelsToCrop", "eastPixelsToCrop", "southPixelsToCrop", "westPixelsToCrop", "colorXY", "rgba2", "colorDiff", "doCrop", "horizontal", "min", "vertical", "widthOfRemainingPixels", "heightOfRemainingPixels", "crop"], "sources": ["../src/index.js"], "sourcesContent": ["/* eslint-disable no-labels */\n\nimport { throwError, isNodePattern } from \"@jimp/utils\";\n\nexport default function pluginCrop(event) {\n  /**\n   * Crops the image at a given point to a give size\n   * @param {number} x the x coordinate to crop form\n   * @param {number} y the y coordinate to crop form\n   * @param w the width of the crop region\n   * @param h the height of the crop region\n   * @param {function(<PERSON><PERSON><PERSON>, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp} this for chaining of methods\n   */\n  event(\"crop\", function (x, y, w, h, cb) {\n    if (typeof x !== \"number\" || typeof y !== \"number\")\n      return throwError.call(this, \"x and y must be numbers\", cb);\n    if (typeof w !== \"number\" || typeof h !== \"number\")\n      return throwError.call(this, \"w and h must be numbers\", cb);\n\n    // round input\n    x = Math.round(x);\n    y = Math.round(y);\n    w = Math.round(w);\n    h = Math.round(h);\n\n    if (x === 0 && w === this.bitmap.width) {\n      // shortcut\n      const start = (w * y + x) << 2;\n      const end = start + ((h * w) << 2);\n\n      this.bitmap.data = this.bitmap.data.slice(start, end);\n    } else {\n      const bitmap = Buffer.allocUnsafe(w * h * 4);\n      let offset = 0;\n\n      this.scanQuiet(x, y, w, h, function (x, y, idx) {\n        const data = this.bitmap.data.readUInt32BE(idx, true);\n        bitmap.writeUInt32BE(data, offset, true);\n        offset += 4;\n      });\n\n      this.bitmap.data = bitmap;\n    }\n\n    this.bitmap.width = w;\n    this.bitmap.height = h;\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  });\n\n  return {\n    class: {\n      /**\n       * Autocrop same color borders from this image\n       * @param {number} tolerance (optional): a percent value of tolerance for pixels color difference (default: 0.0002%)\n       * @param {boolean} cropOnlyFrames (optional): flag to crop only real frames: all 4 sides of the image must have some border (default: true)\n       * @param {function(Error, Jimp)} cb (optional): a callback for when complete (default: no callback)\n       * @returns {Jimp} this for chaining of methods\n       */\n      autocrop(...args) {\n        const w = this.bitmap.width;\n        const h = this.bitmap.height;\n        const minPixelsPerSide = 1; // to avoid cropping completely the image, resulting in an invalid 0 sized image\n\n        let cb; // callback\n        let leaveBorder = 0; // Amount of pixels in border to leave\n        let tolerance = 0.0002; // percent of color difference tolerance (default value)\n        let cropOnlyFrames = true; // flag to force cropping only if the image has a real \"frame\"\n        // i.e. all 4 sides have some border (default value)\n        let cropSymmetric = false; // flag to force cropping top be symmetric.\n        // i.e. north and south / east and west are cropped by the same value\n        let ignoreSides = {\n          north: false,\n          south: false,\n          east: false,\n          west: false,\n        };\n\n        // parse arguments\n        for (let a = 0, len = args.length; a < len; a++) {\n          if (typeof args[a] === \"number\") {\n            // tolerance value passed\n            tolerance = args[a];\n          }\n\n          if (typeof args[a] === \"boolean\") {\n            // cropOnlyFrames value passed\n            cropOnlyFrames = args[a];\n          }\n\n          if (typeof args[a] === \"function\") {\n            // callback value passed\n            cb = args[a];\n          }\n\n          if (typeof args[a] === \"object\") {\n            // config object passed\n            const config = args[a];\n\n            if (typeof config.tolerance !== \"undefined\") {\n              ({ tolerance } = config);\n            }\n\n            if (typeof config.cropOnlyFrames !== \"undefined\") {\n              ({ cropOnlyFrames } = config);\n            }\n\n            if (typeof config.cropSymmetric !== \"undefined\") {\n              ({ cropSymmetric } = config);\n            }\n\n            if (typeof config.leaveBorder !== \"undefined\") {\n              ({ leaveBorder } = config);\n            }\n\n            if (typeof config.ignoreSides !== \"undefined\") {\n              ({ ignoreSides } = config);\n            }\n          }\n        }\n\n        /**\n         * All borders must be of the same color as the top left pixel, to be cropped.\n         * It should be possible to crop borders each with a different color,\n         * but since there are many ways for corners to intersect, it would\n         * introduce unnecessary complexity to the algorithm.\n         */\n\n        // scan each side for same color borders\n        let colorTarget = this.getPixelColor(0, 0); // top left pixel color is the target color\n        const rgba1 = this.constructor.intToRGBA(colorTarget);\n\n        // for north and east sides\n        let northPixelsToCrop = 0;\n        let eastPixelsToCrop = 0;\n        let southPixelsToCrop = 0;\n        let westPixelsToCrop = 0;\n\n        // north side (scan rows from north to south)\n        colorTarget = this.getPixelColor(0, 0);\n        if (!ignoreSides.north) {\n          north: for (let y = 0; y < h - minPixelsPerSide; y++) {\n            for (let x = 0; x < w; x++) {\n              const colorXY = this.getPixelColor(x, y);\n              const rgba2 = this.constructor.intToRGBA(colorXY);\n\n              if (this.constructor.colorDiff(rgba1, rgba2) > tolerance) {\n                // this pixel is too distant from the first one: abort this side scan\n                break north;\n              }\n            }\n\n            // this row contains all pixels with the same color: increment this side pixels to crop\n            northPixelsToCrop++;\n          }\n        }\n\n        // west side (scan columns from west to east)\n        colorTarget = this.getPixelColor(w, 0);\n        if (!ignoreSides.west) {\n          west: for (let x = 0; x < w - minPixelsPerSide; x++) {\n            for (let y = 0 + northPixelsToCrop; y < h; y++) {\n              const colorXY = this.getPixelColor(x, y);\n              const rgba2 = this.constructor.intToRGBA(colorXY);\n\n              if (this.constructor.colorDiff(rgba1, rgba2) > tolerance) {\n                // this pixel is too distant from the first one: abort this side scan\n                break west;\n              }\n            }\n\n            // this column contains all pixels with the same color: increment this side pixels to crop\n            westPixelsToCrop++;\n          }\n        }\n\n        // south side (scan rows from south to north)\n        colorTarget = this.getPixelColor(0, h);\n\n        if (!ignoreSides.south) {\n          south: for (\n            let y = h - 1;\n            y >= northPixelsToCrop + minPixelsPerSide;\n            y--\n          ) {\n            for (let x = w - eastPixelsToCrop - 1; x >= 0; x--) {\n              const colorXY = this.getPixelColor(x, y);\n              const rgba2 = this.constructor.intToRGBA(colorXY);\n\n              if (this.constructor.colorDiff(rgba1, rgba2) > tolerance) {\n                // this pixel is too distant from the first one: abort this side scan\n                break south;\n              }\n            }\n\n            // this row contains all pixels with the same color: increment this side pixels to crop\n            southPixelsToCrop++;\n          }\n        }\n\n        // east side (scan columns from east to west)\n        colorTarget = this.getPixelColor(w, h);\n        if (!ignoreSides.east) {\n          east: for (\n            let x = w - 1;\n            x >= 0 + westPixelsToCrop + minPixelsPerSide;\n            x--\n          ) {\n            for (let y = h - 1; y >= 0 + northPixelsToCrop; y--) {\n              const colorXY = this.getPixelColor(x, y);\n              const rgba2 = this.constructor.intToRGBA(colorXY);\n\n              if (this.constructor.colorDiff(rgba1, rgba2) > tolerance) {\n                // this pixel is too distant from the first one: abort this side scan\n                break east;\n              }\n            }\n\n            // this column contains all pixels with the same color: increment this side pixels to crop\n            eastPixelsToCrop++;\n          }\n        }\n\n        // decide if a crop is needed\n        let doCrop = false;\n\n        // apply leaveBorder\n        westPixelsToCrop -= leaveBorder;\n        eastPixelsToCrop -= leaveBorder;\n        northPixelsToCrop -= leaveBorder;\n        southPixelsToCrop -= leaveBorder;\n\n        if (cropSymmetric) {\n          const horizontal = Math.min(eastPixelsToCrop, westPixelsToCrop);\n          const vertical = Math.min(northPixelsToCrop, southPixelsToCrop);\n          westPixelsToCrop = horizontal;\n          eastPixelsToCrop = horizontal;\n          northPixelsToCrop = vertical;\n          southPixelsToCrop = vertical;\n        }\n\n        // make sure that crops are >= 0\n        westPixelsToCrop = westPixelsToCrop >= 0 ? westPixelsToCrop : 0;\n        eastPixelsToCrop = eastPixelsToCrop >= 0 ? eastPixelsToCrop : 0;\n        northPixelsToCrop = northPixelsToCrop >= 0 ? northPixelsToCrop : 0;\n        southPixelsToCrop = southPixelsToCrop >= 0 ? southPixelsToCrop : 0;\n\n        // safety checks\n        const widthOfRemainingPixels =\n          w - (westPixelsToCrop + eastPixelsToCrop);\n        const heightOfRemainingPixels =\n          h - (southPixelsToCrop + northPixelsToCrop);\n\n        if (cropOnlyFrames) {\n          // crop image if all sides should be cropped\n          doCrop =\n            eastPixelsToCrop !== 0 &&\n            northPixelsToCrop !== 0 &&\n            westPixelsToCrop !== 0 &&\n            southPixelsToCrop !== 0;\n        } else {\n          // crop image if at least one side should be cropped\n          doCrop =\n            eastPixelsToCrop !== 0 ||\n            northPixelsToCrop !== 0 ||\n            westPixelsToCrop !== 0 ||\n            southPixelsToCrop !== 0;\n        }\n\n        if (doCrop) {\n          // do the real crop\n          this.crop(\n            westPixelsToCrop,\n            northPixelsToCrop,\n            widthOfRemainingPixels,\n            heightOfRemainingPixels\n          );\n        }\n\n        if (isNodePattern(cb)) {\n          cb.call(this, null, this);\n        }\n\n        return this;\n      },\n    },\n  };\n}\n"], "mappings": "AAAA;;AAEA,SAASA,UAAU,EAAEC,aAAa,QAAQ,aAAa;AAEvD,eAAe,SAASC,UAAU,CAACC,KAAK,EAAE;EACxC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEA,KAAK,CAAC,MAAM,EAAE,UAAUC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAE;IACtC,IAAI,OAAOJ,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAChD,OAAOL,UAAU,CAACS,IAAI,CAAC,IAAI,EAAE,yBAAyB,EAAED,EAAE,CAAC;IAC7D,IAAI,OAAOF,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAChD,OAAOP,UAAU,CAACS,IAAI,CAAC,IAAI,EAAE,yBAAyB,EAAED,EAAE,CAAC;;IAE7D;IACAJ,CAAC,GAAGM,IAAI,CAACC,KAAK,CAACP,CAAC,CAAC;IACjBC,CAAC,GAAGK,IAAI,CAACC,KAAK,CAACN,CAAC,CAAC;IACjBC,CAAC,GAAGI,IAAI,CAACC,KAAK,CAACL,CAAC,CAAC;IACjBC,CAAC,GAAGG,IAAI,CAACC,KAAK,CAACJ,CAAC,CAAC;IAEjB,IAAIH,CAAC,KAAK,CAAC,IAAIE,CAAC,KAAK,IAAI,CAACM,MAAM,CAACC,KAAK,EAAE;MACtC;MACA,MAAMC,KAAK,GAAIR,CAAC,GAAGD,CAAC,GAAGD,CAAC,IAAK,CAAC;MAC9B,MAAMW,GAAG,GAAGD,KAAK,IAAKP,CAAC,GAAGD,CAAC,IAAK,CAAC,CAAC;MAElC,IAAI,CAACM,MAAM,CAACI,IAAI,GAAG,IAAI,CAACJ,MAAM,CAACI,IAAI,CAACC,KAAK,CAACH,KAAK,EAAEC,GAAG,CAAC;IACvD,CAAC,MAAM;MACL,MAAMH,MAAM,GAAGM,MAAM,CAACC,WAAW,CAACb,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC;MAC5C,IAAIa,MAAM,GAAG,CAAC;MAEd,IAAI,CAACC,SAAS,CAACjB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE,UAAUH,CAAC,EAAEC,CAAC,EAAEiB,GAAG,EAAE;QAC9C,MAAMN,IAAI,GAAG,IAAI,CAACJ,MAAM,CAACI,IAAI,CAACO,YAAY,CAACD,GAAG,EAAE,IAAI,CAAC;QACrDV,MAAM,CAACY,aAAa,CAACR,IAAI,EAAEI,MAAM,EAAE,IAAI,CAAC;QACxCA,MAAM,IAAI,CAAC;MACb,CAAC,CAAC;MAEF,IAAI,CAACR,MAAM,CAACI,IAAI,GAAGJ,MAAM;IAC3B;IAEA,IAAI,CAACA,MAAM,CAACC,KAAK,GAAGP,CAAC;IACrB,IAAI,CAACM,MAAM,CAACa,MAAM,GAAGlB,CAAC;IAEtB,IAAIN,aAAa,CAACO,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb,CAAC,CAAC;EAEF,OAAO;IACLiB,KAAK,EAAE;MACL;AACN;AACA;AACA;AACA;AACA;AACA;MACMC,QAAQ,GAAU;QAChB,MAAMrB,CAAC,GAAG,IAAI,CAACM,MAAM,CAACC,KAAK;QAC3B,MAAMN,CAAC,GAAG,IAAI,CAACK,MAAM,CAACa,MAAM;QAC5B,MAAMG,gBAAgB,GAAG,CAAC,CAAC,CAAC;;QAE5B,IAAIpB,EAAE,CAAC,CAAC;QACR,IAAIqB,WAAW,GAAG,CAAC,CAAC,CAAC;QACrB,IAAIC,SAAS,GAAG,MAAM,CAAC,CAAC;QACxB,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAC;QAC3B;QACA,IAAIC,aAAa,GAAG,KAAK,CAAC,CAAC;QAC3B;QACA,IAAIC,WAAW,GAAG;UAChBC,KAAK,EAAE,KAAK;UACZC,KAAK,EAAE,KAAK;UACZC,IAAI,EAAE,KAAK;UACXC,IAAI,EAAE;QACR,CAAC;;QAED;QAAA,kCAnBUC,IAAI;UAAJA,IAAI;QAAA;QAoBd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGF,IAAI,CAACG,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;UAC/C,IAAI,OAAOD,IAAI,CAACC,CAAC,CAAC,KAAK,QAAQ,EAAE;YAC/B;YACAT,SAAS,GAAGQ,IAAI,CAACC,CAAC,CAAC;UACrB;UAEA,IAAI,OAAOD,IAAI,CAACC,CAAC,CAAC,KAAK,SAAS,EAAE;YAChC;YACAR,cAAc,GAAGO,IAAI,CAACC,CAAC,CAAC;UAC1B;UAEA,IAAI,OAAOD,IAAI,CAACC,CAAC,CAAC,KAAK,UAAU,EAAE;YACjC;YACA/B,EAAE,GAAG8B,IAAI,CAACC,CAAC,CAAC;UACd;UAEA,IAAI,OAAOD,IAAI,CAACC,CAAC,CAAC,KAAK,QAAQ,EAAE;YAC/B;YACA,MAAMG,MAAM,GAAGJ,IAAI,CAACC,CAAC,CAAC;YAEtB,IAAI,OAAOG,MAAM,CAACZ,SAAS,KAAK,WAAW,EAAE;cAC3C,CAAC;gBAAEA;cAAU,CAAC,GAAGY,MAAM;YACzB;YAEA,IAAI,OAAOA,MAAM,CAACX,cAAc,KAAK,WAAW,EAAE;cAChD,CAAC;gBAAEA;cAAe,CAAC,GAAGW,MAAM;YAC9B;YAEA,IAAI,OAAOA,MAAM,CAACV,aAAa,KAAK,WAAW,EAAE;cAC/C,CAAC;gBAAEA;cAAc,CAAC,GAAGU,MAAM;YAC7B;YAEA,IAAI,OAAOA,MAAM,CAACb,WAAW,KAAK,WAAW,EAAE;cAC7C,CAAC;gBAAEA;cAAY,CAAC,GAAGa,MAAM;YAC3B;YAEA,IAAI,OAAOA,MAAM,CAACT,WAAW,KAAK,WAAW,EAAE;cAC7C,CAAC;gBAAEA;cAAY,CAAC,GAAGS,MAAM;YAC3B;UACF;QACF;;QAEA;AACR;AACA;AACA;AACA;AACA;;QAEQ;QACA,IAAIC,WAAW,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5C,MAAMC,KAAK,GAAG,IAAI,CAACC,WAAW,CAACC,SAAS,CAACJ,WAAW,CAAC;;QAErD;QACA,IAAIK,iBAAiB,GAAG,CAAC;QACzB,IAAIC,gBAAgB,GAAG,CAAC;QACxB,IAAIC,iBAAiB,GAAG,CAAC;QACzB,IAAIC,gBAAgB,GAAG,CAAC;;QAExB;QACAR,WAAW,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;QACtC,IAAI,CAACX,WAAW,CAACC,KAAK,EAAE;UACtBA,KAAK,EAAE,KAAK,IAAI7B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,CAAC,GAAGqB,gBAAgB,EAAEvB,CAAC,EAAE,EAAE;YACpD,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,CAAC,EAAEF,CAAC,EAAE,EAAE;cAC1B,MAAMgD,OAAO,GAAG,IAAI,CAACR,aAAa,CAACxC,CAAC,EAAEC,CAAC,CAAC;cACxC,MAAMgD,KAAK,GAAG,IAAI,CAACP,WAAW,CAACC,SAAS,CAACK,OAAO,CAAC;cAEjD,IAAI,IAAI,CAACN,WAAW,CAACQ,SAAS,CAACT,KAAK,EAAEQ,KAAK,CAAC,GAAGvB,SAAS,EAAE;gBACxD;gBACA,MAAMI,KAAK;cACb;YACF;;YAEA;YACAc,iBAAiB,EAAE;UACrB;QACF;;QAEA;QACAL,WAAW,GAAG,IAAI,CAACC,aAAa,CAACtC,CAAC,EAAE,CAAC,CAAC;QACtC,IAAI,CAAC2B,WAAW,CAACI,IAAI,EAAE;UACrBA,IAAI,EAAE,KAAK,IAAIjC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,CAAC,GAAGsB,gBAAgB,EAAExB,CAAC,EAAE,EAAE;YACnD,KAAK,IAAIC,CAAC,GAAG,CAAC,GAAG2C,iBAAiB,EAAE3C,CAAC,GAAGE,CAAC,EAAEF,CAAC,EAAE,EAAE;cAC9C,MAAM+C,OAAO,GAAG,IAAI,CAACR,aAAa,CAACxC,CAAC,EAAEC,CAAC,CAAC;cACxC,MAAMgD,KAAK,GAAG,IAAI,CAACP,WAAW,CAACC,SAAS,CAACK,OAAO,CAAC;cAEjD,IAAI,IAAI,CAACN,WAAW,CAACQ,SAAS,CAACT,KAAK,EAAEQ,KAAK,CAAC,GAAGvB,SAAS,EAAE;gBACxD;gBACA,MAAMO,IAAI;cACZ;YACF;;YAEA;YACAc,gBAAgB,EAAE;UACpB;QACF;;QAEA;QACAR,WAAW,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC,EAAErC,CAAC,CAAC;QAEtC,IAAI,CAAC0B,WAAW,CAACE,KAAK,EAAE;UACtBA,KAAK,EAAE,KACL,IAAI9B,CAAC,GAAGE,CAAC,GAAG,CAAC,EACbF,CAAC,IAAI2C,iBAAiB,GAAGpB,gBAAgB,EACzCvB,CAAC,EAAE,EACH;YACA,KAAK,IAAID,CAAC,GAAGE,CAAC,GAAG2C,gBAAgB,GAAG,CAAC,EAAE7C,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;cAClD,MAAMgD,OAAO,GAAG,IAAI,CAACR,aAAa,CAACxC,CAAC,EAAEC,CAAC,CAAC;cACxC,MAAMgD,KAAK,GAAG,IAAI,CAACP,WAAW,CAACC,SAAS,CAACK,OAAO,CAAC;cAEjD,IAAI,IAAI,CAACN,WAAW,CAACQ,SAAS,CAACT,KAAK,EAAEQ,KAAK,CAAC,GAAGvB,SAAS,EAAE;gBACxD;gBACA,MAAMK,KAAK;cACb;YACF;;YAEA;YACAe,iBAAiB,EAAE;UACrB;QACF;;QAEA;QACAP,WAAW,GAAG,IAAI,CAACC,aAAa,CAACtC,CAAC,EAAEC,CAAC,CAAC;QACtC,IAAI,CAAC0B,WAAW,CAACG,IAAI,EAAE;UACrBA,IAAI,EAAE,KACJ,IAAIhC,CAAC,GAAGE,CAAC,GAAG,CAAC,EACbF,CAAC,IAAI,CAAC,GAAG+C,gBAAgB,GAAGvB,gBAAgB,EAC5CxB,CAAC,EAAE,EACH;YACA,KAAK,IAAIC,CAAC,GAAGE,CAAC,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,GAAG2C,iBAAiB,EAAE3C,CAAC,EAAE,EAAE;cACnD,MAAM+C,OAAO,GAAG,IAAI,CAACR,aAAa,CAACxC,CAAC,EAAEC,CAAC,CAAC;cACxC,MAAMgD,KAAK,GAAG,IAAI,CAACP,WAAW,CAACC,SAAS,CAACK,OAAO,CAAC;cAEjD,IAAI,IAAI,CAACN,WAAW,CAACQ,SAAS,CAACT,KAAK,EAAEQ,KAAK,CAAC,GAAGvB,SAAS,EAAE;gBACxD;gBACA,MAAMM,IAAI;cACZ;YACF;;YAEA;YACAa,gBAAgB,EAAE;UACpB;QACF;;QAEA;QACA,IAAIM,MAAM,GAAG,KAAK;;QAElB;QACAJ,gBAAgB,IAAItB,WAAW;QAC/BoB,gBAAgB,IAAIpB,WAAW;QAC/BmB,iBAAiB,IAAInB,WAAW;QAChCqB,iBAAiB,IAAIrB,WAAW;QAEhC,IAAIG,aAAa,EAAE;UACjB,MAAMwB,UAAU,GAAG9C,IAAI,CAAC+C,GAAG,CAACR,gBAAgB,EAAEE,gBAAgB,CAAC;UAC/D,MAAMO,QAAQ,GAAGhD,IAAI,CAAC+C,GAAG,CAACT,iBAAiB,EAAEE,iBAAiB,CAAC;UAC/DC,gBAAgB,GAAGK,UAAU;UAC7BP,gBAAgB,GAAGO,UAAU;UAC7BR,iBAAiB,GAAGU,QAAQ;UAC5BR,iBAAiB,GAAGQ,QAAQ;QAC9B;;QAEA;QACAP,gBAAgB,GAAGA,gBAAgB,IAAI,CAAC,GAAGA,gBAAgB,GAAG,CAAC;QAC/DF,gBAAgB,GAAGA,gBAAgB,IAAI,CAAC,GAAGA,gBAAgB,GAAG,CAAC;QAC/DD,iBAAiB,GAAGA,iBAAiB,IAAI,CAAC,GAAGA,iBAAiB,GAAG,CAAC;QAClEE,iBAAiB,GAAGA,iBAAiB,IAAI,CAAC,GAAGA,iBAAiB,GAAG,CAAC;;QAElE;QACA,MAAMS,sBAAsB,GAC1BrD,CAAC,IAAI6C,gBAAgB,GAAGF,gBAAgB,CAAC;QAC3C,MAAMW,uBAAuB,GAC3BrD,CAAC,IAAI2C,iBAAiB,GAAGF,iBAAiB,CAAC;QAE7C,IAAIjB,cAAc,EAAE;UAClB;UACAwB,MAAM,GACJN,gBAAgB,KAAK,CAAC,IACtBD,iBAAiB,KAAK,CAAC,IACvBG,gBAAgB,KAAK,CAAC,IACtBD,iBAAiB,KAAK,CAAC;QAC3B,CAAC,MAAM;UACL;UACAK,MAAM,GACJN,gBAAgB,KAAK,CAAC,IACtBD,iBAAiB,KAAK,CAAC,IACvBG,gBAAgB,KAAK,CAAC,IACtBD,iBAAiB,KAAK,CAAC;QAC3B;QAEA,IAAIK,MAAM,EAAE;UACV;UACA,IAAI,CAACM,IAAI,CACPV,gBAAgB,EAChBH,iBAAiB,EACjBW,sBAAsB,EACtBC,uBAAuB,CACxB;QACH;QAEA,IAAI3D,aAAa,CAACO,EAAE,CAAC,EAAE;UACrBA,EAAE,CAACC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;QAC3B;QAEA,OAAO,IAAI;MACb;IACF;EACF,CAAC;AACH"}