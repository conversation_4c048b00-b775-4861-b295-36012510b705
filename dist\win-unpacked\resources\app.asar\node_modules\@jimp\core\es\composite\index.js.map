{"version": 3, "file": "index.js", "names": ["isNodePattern", "throwError", "constants", "compositeModes", "composite", "src", "x", "y", "options", "cb", "constructor", "call", "mode", "opacitySource", "opacityDest", "BLEND_SOURCE_OVER", "blendmode", "Math", "round", "baseImage", "opacity", "scanQuiet", "bitmap", "width", "height", "sx", "sy", "idx", "dstIdx", "getPixelIndex", "EDGE_CROP", "blended", "r", "data", "g", "b", "a", "limit255"], "sources": ["../../src/composite/index.js"], "sourcesContent": ["import { isNodePattern, throwError } from \"@jimp/utils\";\nimport * as constants from \"../constants\";\n\nimport * as compositeModes from \"./composite-modes\";\n\n/**\n * Composites a source image over to this image respecting alpha channels\n * @param {Jimp} src the source Jimp instance\n * @param {number} x the x position to blit the image\n * @param {number} y the y position to blit the image\n * @param {object} options determine what mode to use\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\nexport default function composite(src, x, y, options = {}, cb) {\n  if (typeof options === \"function\") {\n    cb = options;\n    options = {};\n  }\n\n  if (!(src instanceof this.constructor)) {\n    return throwError.call(this, \"The source must be a Jimp image\", cb);\n  }\n\n  if (typeof x !== \"number\" || typeof y !== \"number\") {\n    return throwError.call(this, \"x and y must be numbers\", cb);\n  }\n\n  let { mode, opacitySource, opacityDest } = options;\n\n  if (!mode) {\n    mode = constants.BLEND_SOURCE_OVER;\n  }\n\n  if (\n    typeof opacitySource !== \"number\" ||\n    opacitySource < 0 ||\n    opacitySource > 1\n  ) {\n    opacitySource = 1.0;\n  }\n\n  if (typeof opacityDest !== \"number\" || opacityDest < 0 || opacityDest > 1) {\n    opacityDest = 1.0;\n  }\n\n  // eslint-disable-next-line import/namespace\n  const blendmode = compositeModes[mode];\n\n  // round input\n  x = Math.round(x);\n  y = Math.round(y);\n\n  const baseImage = this;\n\n  if (opacityDest !== 1.0) {\n    baseImage.opacity(opacityDest);\n  }\n\n  src.scanQuiet(\n    0,\n    0,\n    src.bitmap.width,\n    src.bitmap.height,\n    function (sx, sy, idx) {\n      const dstIdx = baseImage.getPixelIndex(\n        x + sx,\n        y + sy,\n        constants.EDGE_CROP\n      );\n\n      if (dstIdx === -1) {\n        // Skip target pixels outside of dst\n        return;\n      }\n\n      const blended = blendmode(\n        {\n          r: this.bitmap.data[idx + 0] / 255,\n          g: this.bitmap.data[idx + 1] / 255,\n          b: this.bitmap.data[idx + 2] / 255,\n          a: this.bitmap.data[idx + 3] / 255,\n        },\n        {\n          r: baseImage.bitmap.data[dstIdx + 0] / 255,\n          g: baseImage.bitmap.data[dstIdx + 1] / 255,\n          b: baseImage.bitmap.data[dstIdx + 2] / 255,\n          a: baseImage.bitmap.data[dstIdx + 3] / 255,\n        },\n        opacitySource\n      );\n\n      baseImage.bitmap.data[dstIdx + 0] = this.constructor.limit255(\n        blended.r * 255\n      );\n      baseImage.bitmap.data[dstIdx + 1] = this.constructor.limit255(\n        blended.g * 255\n      );\n      baseImage.bitmap.data[dstIdx + 2] = this.constructor.limit255(\n        blended.b * 255\n      );\n      baseImage.bitmap.data[dstIdx + 3] = this.constructor.limit255(\n        blended.a * 255\n      );\n    }\n  );\n\n  if (isNodePattern(cb)) {\n    cb.call(this, null, this);\n  }\n\n  return this;\n}\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,UAAU,QAAQ,aAAa;AACvD,OAAO,KAAKC,SAAS,MAAM,cAAc;AAEzC,OAAO,KAAKC,cAAc,MAAM,mBAAmB;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,SAAS,CAACC,GAAG,EAAEC,CAAC,EAAEC,CAAC,EAAoB;EAAA,IAAlBC,OAAO,uEAAG,CAAC,CAAC;EAAA,IAAEC,EAAE;EAC3D,IAAI,OAAOD,OAAO,KAAK,UAAU,EAAE;IACjCC,EAAE,GAAGD,OAAO;IACZA,OAAO,GAAG,CAAC,CAAC;EACd;EAEA,IAAI,EAAEH,GAAG,YAAY,IAAI,CAACK,WAAW,CAAC,EAAE;IACtC,OAAOT,UAAU,CAACU,IAAI,CAAC,IAAI,EAAE,iCAAiC,EAAEF,EAAE,CAAC;EACrE;EAEA,IAAI,OAAOH,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE;IAClD,OAAON,UAAU,CAACU,IAAI,CAAC,IAAI,EAAE,yBAAyB,EAAEF,EAAE,CAAC;EAC7D;EAEA,IAAI;IAAEG,IAAI;IAAEC,aAAa;IAAEC;EAAY,CAAC,GAAGN,OAAO;EAElD,IAAI,CAACI,IAAI,EAAE;IACTA,IAAI,GAAGV,SAAS,CAACa,iBAAiB;EACpC;EAEA,IACE,OAAOF,aAAa,KAAK,QAAQ,IACjCA,aAAa,GAAG,CAAC,IACjBA,aAAa,GAAG,CAAC,EACjB;IACAA,aAAa,GAAG,GAAG;EACrB;EAEA,IAAI,OAAOC,WAAW,KAAK,QAAQ,IAAIA,WAAW,GAAG,CAAC,IAAIA,WAAW,GAAG,CAAC,EAAE;IACzEA,WAAW,GAAG,GAAG;EACnB;;EAEA;EACA,MAAME,SAAS,GAAGb,cAAc,CAACS,IAAI,CAAC;;EAEtC;EACAN,CAAC,GAAGW,IAAI,CAACC,KAAK,CAACZ,CAAC,CAAC;EACjBC,CAAC,GAAGU,IAAI,CAACC,KAAK,CAACX,CAAC,CAAC;EAEjB,MAAMY,SAAS,GAAG,IAAI;EAEtB,IAAIL,WAAW,KAAK,GAAG,EAAE;IACvBK,SAAS,CAACC,OAAO,CAACN,WAAW,CAAC;EAChC;EAEAT,GAAG,CAACgB,SAAS,CACX,CAAC,EACD,CAAC,EACDhB,GAAG,CAACiB,MAAM,CAACC,KAAK,EAChBlB,GAAG,CAACiB,MAAM,CAACE,MAAM,EACjB,UAAUC,EAAE,EAAEC,EAAE,EAAEC,GAAG,EAAE;IACrB,MAAMC,MAAM,GAAGT,SAAS,CAACU,aAAa,CACpCvB,CAAC,GAAGmB,EAAE,EACNlB,CAAC,GAAGmB,EAAE,EACNxB,SAAS,CAAC4B,SAAS,CACpB;IAED,IAAIF,MAAM,KAAK,CAAC,CAAC,EAAE;MACjB;MACA;IACF;IAEA,MAAMG,OAAO,GAAGf,SAAS,CACvB;MACEgB,CAAC,EAAE,IAAI,CAACV,MAAM,CAACW,IAAI,CAACN,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG;MAClCO,CAAC,EAAE,IAAI,CAACZ,MAAM,CAACW,IAAI,CAACN,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG;MAClCQ,CAAC,EAAE,IAAI,CAACb,MAAM,CAACW,IAAI,CAACN,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG;MAClCS,CAAC,EAAE,IAAI,CAACd,MAAM,CAACW,IAAI,CAACN,GAAG,GAAG,CAAC,CAAC,GAAG;IACjC,CAAC,EACD;MACEK,CAAC,EAAEb,SAAS,CAACG,MAAM,CAACW,IAAI,CAACL,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG;MAC1CM,CAAC,EAAEf,SAAS,CAACG,MAAM,CAACW,IAAI,CAACL,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG;MAC1CO,CAAC,EAAEhB,SAAS,CAACG,MAAM,CAACW,IAAI,CAACL,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG;MAC1CQ,CAAC,EAAEjB,SAAS,CAACG,MAAM,CAACW,IAAI,CAACL,MAAM,GAAG,CAAC,CAAC,GAAG;IACzC,CAAC,EACDf,aAAa,CACd;IAEDM,SAAS,CAACG,MAAM,CAACW,IAAI,CAACL,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAClB,WAAW,CAAC2B,QAAQ,CAC3DN,OAAO,CAACC,CAAC,GAAG,GAAG,CAChB;IACDb,SAAS,CAACG,MAAM,CAACW,IAAI,CAACL,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAClB,WAAW,CAAC2B,QAAQ,CAC3DN,OAAO,CAACG,CAAC,GAAG,GAAG,CAChB;IACDf,SAAS,CAACG,MAAM,CAACW,IAAI,CAACL,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAClB,WAAW,CAAC2B,QAAQ,CAC3DN,OAAO,CAACI,CAAC,GAAG,GAAG,CAChB;IACDhB,SAAS,CAACG,MAAM,CAACW,IAAI,CAACL,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAClB,WAAW,CAAC2B,QAAQ,CAC3DN,OAAO,CAACK,CAAC,GAAG,GAAG,CAChB;EACH,CAAC,CACF;EAED,IAAIpC,aAAa,CAACS,EAAE,CAAC,EAAE;IACrBA,EAAE,CAACE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC3B;EAEA,OAAO,IAAI;AACb"}