directories:
  output: dist
  buildResources: build
appId: com.ttm.image-to-pdf
productName: 图片转PDF工具
files:
  - filter:
      - src/**/*
      - node_modules/**/*
      - package.json
win:
  target:
    - target: nsis
      arch:
        - x64
  requestedExecutionLevel: asInvoker
  sign: null
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: 图片转PDF工具
extraResources:
  - from: assets/
    to: assets/
    filter:
      - '**/*'
electronVersion: 27.3.11
