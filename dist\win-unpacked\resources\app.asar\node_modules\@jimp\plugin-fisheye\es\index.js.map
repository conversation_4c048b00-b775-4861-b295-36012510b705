{"version": 3, "file": "index.js", "names": ["isNodePattern", "fisheye", "options", "r", "cb", "source", "clone<PERSON>uiet", "width", "height", "bitmap", "scanQuiet", "x", "y", "hx", "hy", "Math", "sqrt", "pow", "rn", "cosA", "sinA", "newX", "round", "newY", "color", "getPixelColor", "setPixelColor", "call"], "sources": ["../src/index.js"], "sourcesContent": ["import { isNodePattern } from \"@jimp/utils\";\n\n/**\n * Creates a circle out of an image.\n * @param {object} options (optional) r: radius of effect\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {<PERSON><PERSON>} this for chaining of methods\n */\nexport default () => ({\n  fisheye(options = { r: 2.5 }, cb) {\n    if (typeof options === \"function\") {\n      cb = options;\n      options = { r: 2.5 };\n    }\n\n    const source = this.cloneQuiet();\n    const { width, height } = source.bitmap;\n\n    source.scanQuiet(0, 0, width, height, (x, y) => {\n      const hx = x / width;\n      const hy = y / height;\n      const r = Math.sqrt(Math.pow(hx - 0.5, 2) + Math.pow(hy - 0.5, 2));\n      const rn = 2 * Math.pow(r, options.r);\n      const cosA = (hx - 0.5) / r;\n      const sinA = (hy - 0.5) / r;\n      const newX = Math.round((rn * cosA + 0.5) * width);\n      const newY = Math.round((rn * sinA + 0.5) * height);\n      const color = source.getPixelColor(newX, newY);\n\n      this.setPixelColor(color, x, y);\n    });\n\n    /* Set center pixel color, otherwise it will be transparent */\n    this.setPixelColor(\n      source.getPixelColor(width / 2, height / 2),\n      width / 2,\n      height / 2\n    );\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n});\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,aAAa;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA,gBAAe,OAAO;EACpBC,OAAO,GAA2B;IAAA,IAA1BC,OAAO,uEAAG;MAAEC,CAAC,EAAE;IAAI,CAAC;IAAA,IAAEC,EAAE;IAC9B,IAAI,OAAOF,OAAO,KAAK,UAAU,EAAE;MACjCE,EAAE,GAAGF,OAAO;MACZA,OAAO,GAAG;QAAEC,CAAC,EAAE;MAAI,CAAC;IACtB;IAEA,MAAME,MAAM,GAAG,IAAI,CAACC,UAAU,EAAE;IAChC,MAAM;MAAEC,KAAK;MAAEC;IAAO,CAAC,GAAGH,MAAM,CAACI,MAAM;IAEvCJ,MAAM,CAACK,SAAS,CAAC,CAAC,EAAE,CAAC,EAAEH,KAAK,EAAEC,MAAM,EAAE,CAACG,CAAC,EAAEC,CAAC,KAAK;MAC9C,MAAMC,EAAE,GAAGF,CAAC,GAAGJ,KAAK;MACpB,MAAMO,EAAE,GAAGF,CAAC,GAAGJ,MAAM;MACrB,MAAML,CAAC,GAAGY,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,GAAG,CAACJ,EAAE,GAAG,GAAG,EAAE,CAAC,CAAC,GAAGE,IAAI,CAACE,GAAG,CAACH,EAAE,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;MAClE,MAAMI,EAAE,GAAG,CAAC,GAAGH,IAAI,CAACE,GAAG,CAACd,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;MACrC,MAAMgB,IAAI,GAAG,CAACN,EAAE,GAAG,GAAG,IAAIV,CAAC;MAC3B,MAAMiB,IAAI,GAAG,CAACN,EAAE,GAAG,GAAG,IAAIX,CAAC;MAC3B,MAAMkB,IAAI,GAAGN,IAAI,CAACO,KAAK,CAAC,CAACJ,EAAE,GAAGC,IAAI,GAAG,GAAG,IAAIZ,KAAK,CAAC;MAClD,MAAMgB,IAAI,GAAGR,IAAI,CAACO,KAAK,CAAC,CAACJ,EAAE,GAAGE,IAAI,GAAG,GAAG,IAAIZ,MAAM,CAAC;MACnD,MAAMgB,KAAK,GAAGnB,MAAM,CAACoB,aAAa,CAACJ,IAAI,EAAEE,IAAI,CAAC;MAE9C,IAAI,CAACG,aAAa,CAACF,KAAK,EAAEb,CAAC,EAAEC,CAAC,CAAC;IACjC,CAAC,CAAC;;IAEF;IACA,IAAI,CAACc,aAAa,CAChBrB,MAAM,CAACoB,aAAa,CAAClB,KAAK,GAAG,CAAC,EAAEC,MAAM,GAAG,CAAC,CAAC,EAC3CD,KAAK,GAAG,CAAC,EACTC,MAAM,GAAG,CAAC,CACX;IAED,IAAIR,aAAa,CAACI,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACuB,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb;AACF,CAAC,CAAC"}