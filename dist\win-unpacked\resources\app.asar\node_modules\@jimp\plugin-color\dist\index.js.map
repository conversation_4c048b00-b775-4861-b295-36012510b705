{"version": 3, "file": "index.js", "names": ["applyKernel", "im", "kernel", "x", "y", "value", "size", "length", "kx", "ky", "idx", "getPixelIndex", "bitmap", "data", "isDef", "v", "greyscale", "cb", "scanQuiet", "width", "height", "grey", "parseInt", "isNodePattern", "call", "mix", "clr", "clr2", "p", "r", "g", "b", "colorFn", "actions", "Array", "isArray", "throwError", "map", "action", "apply", "params", "tinyColor", "toRgb", "colorModifier", "i", "amount", "constructor", "limit255", "for<PERSON>ach", "ColorActionName", "Object", "freeze", "LIGHTEN", "BRIGHTEN", "DARKEN", "DESATURATE", "SATURATE", "GREYSCALE", "SPIN", "HUE", "MIX", "TINT", "SHADE", "XOR", "RED", "GREEN", "BLUE", "brightness", "val", "contrast", "factor", "adjust", "Math", "floor", "posterize", "n", "grayscale", "opacity", "f", "sepia", "red", "green", "blue", "fade", "convolution", "edgeHandling", "EDGE_EXTEND", "newData", "<PERSON><PERSON><PERSON>", "from", "kRows", "kCols", "rowEnd", "colEnd", "rowIni", "colIni", "weight", "rSum", "gSum", "bSum", "ri", "gi", "bi", "xi", "yi", "idxi", "row", "col", "opaque", "pixelate", "w", "h", "source", "clone<PERSON>uiet", "xx", "yx", "convolute", "color", "colour"], "sources": ["../src/index.js"], "sourcesContent": ["import tinyColor from \"tinycolor2\";\nimport { throwError, isNodePattern } from \"@jimp/utils\";\n\nfunction applyKernel(im, kernel, x, y) {\n  const value = [0, 0, 0];\n  const size = (kernel.length - 1) / 2;\n\n  for (let kx = 0; kx < kernel.length; kx += 1) {\n    for (let ky = 0; ky < kernel[kx].length; ky += 1) {\n      const idx = im.getPixelIndex(x + kx - size, y + ky - size);\n\n      value[0] += im.bitmap.data[idx] * kernel[kx][ky];\n      value[1] += im.bitmap.data[idx + 1] * kernel[kx][ky];\n      value[2] += im.bitmap.data[idx + 2] * kernel[kx][ky];\n    }\n  }\n\n  return value;\n}\n\nconst isDef = (v) => typeof v !== \"undefined\" && v !== null;\n\nfunction greyscale(cb) {\n  this.scanQuiet(\n    0,\n    0,\n    this.bitmap.width,\n    this.bitmap.height,\n    function (x, y, idx) {\n      const grey = parseInt(\n        0.2126 * this.bitmap.data[idx] +\n          0.7152 * this.bitmap.data[idx + 1] +\n          0.0722 * this.bitmap.data[idx + 2],\n        10\n      );\n\n      this.bitmap.data[idx] = grey;\n      this.bitmap.data[idx + 1] = grey;\n      this.bitmap.data[idx + 2] = grey;\n    }\n  );\n\n  if (isNodePattern(cb)) {\n    cb.call(this, null, this);\n  }\n\n  return this;\n}\n\nfunction mix(clr, clr2, p = 50) {\n  return {\n    r: (clr2.r - clr.r) * (p / 100) + clr.r,\n    g: (clr2.g - clr.g) * (p / 100) + clr.g,\n    b: (clr2.b - clr.b) * (p / 100) + clr.b,\n  };\n}\n\nfunction colorFn(actions, cb) {\n  if (!actions || !Array.isArray(actions)) {\n    return throwError.call(this, \"actions must be an array\", cb);\n  }\n\n  actions = actions.map((action) => {\n    if (action.apply === \"xor\" || action.apply === \"mix\") {\n      action.params[0] = tinyColor(action.params[0]).toRgb();\n    }\n\n    return action;\n  });\n\n  this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, (x, y, idx) => {\n    let clr = {\n      r: this.bitmap.data[idx],\n      g: this.bitmap.data[idx + 1],\n      b: this.bitmap.data[idx + 2],\n    };\n\n    const colorModifier = (i, amount) =>\n      this.constructor.limit255(clr[i] + amount);\n\n    actions.forEach((action) => {\n      if (action.apply === \"mix\") {\n        clr = mix(clr, action.params[0], action.params[1]);\n      } else if (action.apply === \"tint\") {\n        clr = mix(clr, { r: 255, g: 255, b: 255 }, action.params[0]);\n      } else if (action.apply === \"shade\") {\n        clr = mix(clr, { r: 0, g: 0, b: 0 }, action.params[0]);\n      } else if (action.apply === \"xor\") {\n        clr = {\n          r: clr.r ^ action.params[0].r,\n          g: clr.g ^ action.params[0].g,\n          b: clr.b ^ action.params[0].b,\n        };\n      } else if (action.apply === \"red\") {\n        clr.r = colorModifier(\"r\", action.params[0]);\n      } else if (action.apply === \"green\") {\n        clr.g = colorModifier(\"g\", action.params[0]);\n      } else if (action.apply === \"blue\") {\n        clr.b = colorModifier(\"b\", action.params[0]);\n      } else {\n        if (action.apply === \"hue\") {\n          action.apply = \"spin\";\n        }\n\n        clr = tinyColor(clr);\n\n        if (!clr[action.apply]) {\n          return throwError.call(\n            this,\n            \"action \" + action.apply + \" not supported\",\n            cb\n          );\n        }\n\n        clr = clr[action.apply](...action.params).toRgb();\n      }\n    });\n\n    this.bitmap.data[idx] = clr.r;\n    this.bitmap.data[idx + 1] = clr.g;\n    this.bitmap.data[idx + 2] = clr.b;\n  });\n\n  if (isNodePattern(cb)) {\n    cb.call(this, null, this);\n  }\n\n  return this;\n}\n\nexport const ColorActionName = Object.freeze({\n  LIGHTEN: \"lighten\",\n  BRIGHTEN: \"brighten\",\n  DARKEN: \"darken\",\n  DESATURATE: \"desaturate\",\n  SATURATE: \"saturate\",\n  GREYSCALE: \"greyscale\",\n  SPIN: \"spin\",\n  HUE: \"hue\",\n  MIX: \"mix\",\n  TINT: \"tint\",\n  SHADE: \"shade\",\n  XOR: \"xor\",\n  RED: \"red\",\n  GREEN: \"green\",\n  BLUE: \"blue\",\n});\n\nexport default () => ({\n  /**\n   * Adjusts the brightness of the image\n   * @param {number} val the amount to adjust the brightness, a number between -1 and +1\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  brightness(val, cb) {\n    if (typeof val !== \"number\") {\n      return throwError.call(this, \"val must be numbers\", cb);\n    }\n\n    if (val < -1 || val > +1) {\n      return throwError.call(\n        this,\n        \"val must be a number between -1 and +1\",\n        cb\n      );\n    }\n\n    this.scanQuiet(\n      0,\n      0,\n      this.bitmap.width,\n      this.bitmap.height,\n      function (x, y, idx) {\n        if (val < 0.0) {\n          this.bitmap.data[idx] *= 1 + val;\n          this.bitmap.data[idx + 1] *= 1 + val;\n          this.bitmap.data[idx + 2] *= 1 + val;\n        } else {\n          this.bitmap.data[idx] += (255 - this.bitmap.data[idx]) * val;\n          this.bitmap.data[idx + 1] += (255 - this.bitmap.data[idx + 1]) * val;\n          this.bitmap.data[idx + 2] += (255 - this.bitmap.data[idx + 2]) * val;\n        }\n      }\n    );\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Adjusts the contrast of the image\n   * @param {number} val the amount to adjust the contrast, a number between -1 and +1\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  contrast(val, cb) {\n    if (typeof val !== \"number\") {\n      return throwError.call(this, \"val must be numbers\", cb);\n    }\n\n    if (val < -1 || val > +1) {\n      return throwError.call(\n        this,\n        \"val must be a number between -1 and +1\",\n        cb\n      );\n    }\n\n    const factor = (val + 1) / (1 - val);\n\n    function adjust(value) {\n      value = Math.floor(factor * (value - 127) + 127);\n\n      return value < 0 ? 0 : value > 255 ? 255 : value;\n    }\n\n    this.scanQuiet(\n      0,\n      0,\n      this.bitmap.width,\n      this.bitmap.height,\n      function (x, y, idx) {\n        this.bitmap.data[idx] = adjust(this.bitmap.data[idx]);\n        this.bitmap.data[idx + 1] = adjust(this.bitmap.data[idx + 1]);\n        this.bitmap.data[idx + 2] = adjust(this.bitmap.data[idx + 2]);\n      }\n    );\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Apply a posterize effect\n   * @param {number} n the amount to adjust the contrast, minimum threshold is two\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  posterize(n, cb) {\n    if (typeof n !== \"number\") {\n      return throwError.call(this, \"n must be numbers\", cb);\n    }\n\n    if (n < 2) {\n      n = 2;\n    } // minimum of 2 levels\n\n    this.scanQuiet(\n      0,\n      0,\n      this.bitmap.width,\n      this.bitmap.height,\n      function (x, y, idx) {\n        this.bitmap.data[idx] =\n          (Math.floor((this.bitmap.data[idx] / 255) * (n - 1)) / (n - 1)) * 255;\n        this.bitmap.data[idx + 1] =\n          (Math.floor((this.bitmap.data[idx + 1] / 255) * (n - 1)) / (n - 1)) *\n          255;\n        this.bitmap.data[idx + 2] =\n          (Math.floor((this.bitmap.data[idx + 2] / 255) * (n - 1)) / (n - 1)) *\n          255;\n      }\n    );\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Removes colour from the image using ITU Rec 709 luminance values\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  greyscale,\n\n  // Alias of greyscale for our American friends\n  grayscale: greyscale,\n\n  /**\n   * Multiplies the opacity of each pixel by a factor between 0 and 1\n   * @param {number} f A number, the factor by which to multiply the opacity of each pixel\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  opacity(f, cb) {\n    if (typeof f !== \"number\")\n      return throwError.call(this, \"f must be a number\", cb);\n    if (f < 0 || f > 1)\n      return throwError.call(this, \"f must be a number from 0 to 1\", cb);\n\n    this.scanQuiet(\n      0,\n      0,\n      this.bitmap.width,\n      this.bitmap.height,\n      function (x, y, idx) {\n        const v = this.bitmap.data[idx + 3] * f;\n        this.bitmap.data[idx + 3] = v;\n      }\n    );\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Applies a sepia tone to the image\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  sepia(cb) {\n    this.scanQuiet(\n      0,\n      0,\n      this.bitmap.width,\n      this.bitmap.height,\n      function (x, y, idx) {\n        let red = this.bitmap.data[idx];\n        let green = this.bitmap.data[idx + 1];\n        let blue = this.bitmap.data[idx + 2];\n\n        red = red * 0.393 + green * 0.769 + blue * 0.189;\n        green = red * 0.349 + green * 0.686 + blue * 0.168;\n        blue = red * 0.272 + green * 0.534 + blue * 0.131;\n\n        this.bitmap.data[idx] = red < 255 ? red : 255;\n        this.bitmap.data[idx + 1] = green < 255 ? green : 255;\n        this.bitmap.data[idx + 2] = blue < 255 ? blue : 255;\n      }\n    );\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Fades each pixel by a factor between 0 and 1\n   * @param {number} f A number from 0 to 1. 0 will haven no effect. 1 will turn the image completely transparent.\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  fade(f, cb) {\n    if (typeof f !== \"number\") {\n      return throwError.call(this, \"f must be a number\", cb);\n    }\n\n    if (f < 0 || f > 1) {\n      return throwError.call(this, \"f must be a number from 0 to 1\", cb);\n    }\n\n    // this method is an alternative to opacity (which may be deprecated)\n    this.opacity(1 - f);\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Adds each element of the image to its local neighbors, weighted by the kernel\n   * @param {array} kernel a matrix to weight the neighbors sum\n   * @param {number} edgeHandling (optional) define how to sum pixels from outside the border\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  convolution(kernel, edgeHandling, cb) {\n    if (typeof edgeHandling === \"function\" && typeof cb === \"undefined\") {\n      cb = edgeHandling;\n      edgeHandling = null;\n    }\n\n    if (!edgeHandling) {\n      edgeHandling = this.constructor.EDGE_EXTEND;\n    }\n\n    const newData = Buffer.from(this.bitmap.data);\n    const kRows = kernel.length;\n    const kCols = kernel[0].length;\n    const rowEnd = Math.floor(kRows / 2);\n    const colEnd = Math.floor(kCols / 2);\n    const rowIni = -rowEnd;\n    const colIni = -colEnd;\n\n    let weight;\n    let rSum;\n    let gSum;\n    let bSum;\n    let ri;\n    let gi;\n    let bi;\n    let xi;\n    let yi;\n    let idxi;\n\n    this.scanQuiet(\n      0,\n      0,\n      this.bitmap.width,\n      this.bitmap.height,\n      function (x, y, idx) {\n        bSum = 0;\n        gSum = 0;\n        rSum = 0;\n\n        for (let row = rowIni; row <= rowEnd; row++) {\n          for (let col = colIni; col <= colEnd; col++) {\n            xi = x + col;\n            yi = y + row;\n            weight = kernel[row + rowEnd][col + colEnd];\n            idxi = this.getPixelIndex(xi, yi, edgeHandling);\n\n            if (idxi === -1) {\n              bi = 0;\n              gi = 0;\n              ri = 0;\n            } else {\n              ri = this.bitmap.data[idxi + 0];\n              gi = this.bitmap.data[idxi + 1];\n              bi = this.bitmap.data[idxi + 2];\n            }\n\n            rSum += weight * ri;\n            gSum += weight * gi;\n            bSum += weight * bi;\n          }\n        }\n\n        if (rSum < 0) {\n          rSum = 0;\n        }\n\n        if (gSum < 0) {\n          gSum = 0;\n        }\n\n        if (bSum < 0) {\n          bSum = 0;\n        }\n\n        if (rSum > 255) {\n          rSum = 255;\n        }\n\n        if (gSum > 255) {\n          gSum = 255;\n        }\n\n        if (bSum > 255) {\n          bSum = 255;\n        }\n\n        newData[idx + 0] = rSum;\n        newData[idx + 1] = gSum;\n        newData[idx + 2] = bSum;\n      }\n    );\n\n    this.bitmap.data = newData;\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Set the alpha channel on every pixel to fully opaque\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  opaque(cb) {\n    this.scanQuiet(\n      0,\n      0,\n      this.bitmap.width,\n      this.bitmap.height,\n      function (x, y, idx) {\n        this.bitmap.data[idx + 3] = 255;\n      }\n    );\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Pixelates the image or a region\n   * @param {number} size the size of the pixels\n   * @param {number} x (optional) the x position of the region to pixelate\n   * @param {number} y (optional) the y position of the region to pixelate\n   * @param {number} w (optional) the width of the region to pixelate\n   * @param {number} h (optional) the height of the region to pixelate\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  pixelate(size, x, y, w, h, cb) {\n    if (typeof x === \"function\") {\n      cb = x;\n      h = null;\n      w = null;\n      y = null;\n      x = null;\n    } else {\n      if (typeof size !== \"number\") {\n        return throwError.call(this, \"size must be a number\", cb);\n      }\n\n      if (isDef(x) && typeof x !== \"number\") {\n        return throwError.call(this, \"x must be a number\", cb);\n      }\n\n      if (isDef(y) && typeof y !== \"number\") {\n        return throwError.call(this, \"y must be a number\", cb);\n      }\n\n      if (isDef(w) && typeof w !== \"number\") {\n        return throwError.call(this, \"w must be a number\", cb);\n      }\n\n      if (isDef(h) && typeof h !== \"number\") {\n        return throwError.call(this, \"h must be a number\", cb);\n      }\n    }\n\n    const kernel = [\n      [1 / 16, 2 / 16, 1 / 16],\n      [2 / 16, 4 / 16, 2 / 16],\n      [1 / 16, 2 / 16, 1 / 16],\n    ];\n\n    x = x || 0;\n    y = y || 0;\n    w = isDef(w) ? w : this.bitmap.width - x;\n    h = isDef(h) ? h : this.bitmap.height - y;\n\n    const source = this.cloneQuiet();\n\n    this.scanQuiet(x, y, w, h, function (xx, yx, idx) {\n      xx = size * Math.floor(xx / size);\n      yx = size * Math.floor(yx / size);\n\n      const value = applyKernel(source, kernel, xx, yx);\n\n      this.bitmap.data[idx] = value[0];\n      this.bitmap.data[idx + 1] = value[1];\n      this.bitmap.data[idx + 2] = value[2];\n    });\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Applies a convolution kernel to the image or a region\n   * @param {array} kernel the convolution kernel\n   * @param {number} x (optional) the x position of the region to apply convolution to\n   * @param {number} y (optional) the y position of the region to apply convolution to\n   * @param {number} w (optional) the width of the region to apply convolution to\n   * @param {number} h (optional) the height of the region to apply convolution to\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp} this for chaining of methods\n   */\n  convolute(kernel, x, y, w, h, cb) {\n    if (!Array.isArray(kernel))\n      return throwError.call(this, \"the kernel must be an array\", cb);\n\n    if (typeof x === \"function\") {\n      cb = x;\n      x = null;\n      y = null;\n      w = null;\n      h = null;\n    } else {\n      if (isDef(x) && typeof x !== \"number\") {\n        return throwError.call(this, \"x must be a number\", cb);\n      }\n\n      if (isDef(y) && typeof y !== \"number\") {\n        return throwError.call(this, \"y must be a number\", cb);\n      }\n\n      if (isDef(w) && typeof w !== \"number\") {\n        return throwError.call(this, \"w must be a number\", cb);\n      }\n\n      if (isDef(h) && typeof h !== \"number\") {\n        return throwError.call(this, \"h must be a number\", cb);\n      }\n    }\n\n    x = isDef(x) ? x : 0;\n    y = isDef(y) ? y : 0;\n    w = isDef(w) ? w : this.bitmap.width - x;\n    h = isDef(h) ? h : this.bitmap.height - y;\n\n    const source = this.cloneQuiet();\n\n    this.scanQuiet(x, y, w, h, function (xx, yx, idx) {\n      const value = applyKernel(source, kernel, xx, yx);\n\n      this.bitmap.data[idx] = this.constructor.limit255(value[0]);\n      this.bitmap.data[idx + 1] = this.constructor.limit255(value[1]);\n      this.bitmap.data[idx + 2] = this.constructor.limit255(value[2]);\n    });\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Apply multiple color modification rules\n   * @param {array} actions list of color modification rules, in following format: { apply: '<rule-name>', params: [ <rule-parameters> ]  }\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  color: colorFn,\n  colour: colorFn,\n});\n"], "mappings": ";;;;;;AAAA;AACA;AAAwD;AAExD,SAASA,WAAW,CAACC,EAAE,EAAEC,MAAM,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACrC,MAAMC,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACvB,MAAMC,IAAI,GAAG,CAACJ,MAAM,CAACK,MAAM,GAAG,CAAC,IAAI,CAAC;EAEpC,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGN,MAAM,CAACK,MAAM,EAAEC,EAAE,IAAI,CAAC,EAAE;IAC5C,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGP,MAAM,CAACM,EAAE,CAAC,CAACD,MAAM,EAAEE,EAAE,IAAI,CAAC,EAAE;MAChD,MAAMC,GAAG,GAAGT,EAAE,CAACU,aAAa,CAACR,CAAC,GAAGK,EAAE,GAAGF,IAAI,EAAEF,CAAC,GAAGK,EAAE,GAAGH,IAAI,CAAC;MAE1DD,KAAK,CAAC,CAAC,CAAC,IAAIJ,EAAE,CAACW,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,GAAGR,MAAM,CAACM,EAAE,CAAC,CAACC,EAAE,CAAC;MAChDJ,KAAK,CAAC,CAAC,CAAC,IAAIJ,EAAE,CAACW,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAGR,MAAM,CAACM,EAAE,CAAC,CAACC,EAAE,CAAC;MACpDJ,KAAK,CAAC,CAAC,CAAC,IAAIJ,EAAE,CAACW,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAGR,MAAM,CAACM,EAAE,CAAC,CAACC,EAAE,CAAC;IACtD;EACF;EAEA,OAAOJ,KAAK;AACd;AAEA,MAAMS,KAAK,GAAIC,CAAC,IAAK,OAAOA,CAAC,KAAK,WAAW,IAAIA,CAAC,KAAK,IAAI;AAE3D,SAASC,SAAS,CAACC,EAAE,EAAE;EACrB,IAAI,CAACC,SAAS,CACZ,CAAC,EACD,CAAC,EACD,IAAI,CAACN,MAAM,CAACO,KAAK,EACjB,IAAI,CAACP,MAAM,CAACQ,MAAM,EAClB,UAAUjB,CAAC,EAAEC,CAAC,EAAEM,GAAG,EAAE;IACnB,MAAMW,IAAI,GAAGC,QAAQ,CACnB,MAAM,GAAG,IAAI,CAACV,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,GAC5B,MAAM,GAAG,IAAI,CAACE,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAClC,MAAM,GAAG,IAAI,CAACE,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,EACpC,EAAE,CACH;IAED,IAAI,CAACE,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,GAAGW,IAAI;IAC5B,IAAI,CAACT,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAGW,IAAI;IAChC,IAAI,CAACT,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAGW,IAAI;EAClC,CAAC,CACF;EAED,IAAI,IAAAE,oBAAa,EAACN,EAAE,CAAC,EAAE;IACrBA,EAAE,CAACO,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC3B;EAEA,OAAO,IAAI;AACb;AAEA,SAASC,GAAG,CAACC,GAAG,EAAEC,IAAI,EAAU;EAAA,IAARC,CAAC,uEAAG,EAAE;EAC5B,OAAO;IACLC,CAAC,EAAE,CAACF,IAAI,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC,KAAKD,CAAC,GAAG,GAAG,CAAC,GAAGF,GAAG,CAACG,CAAC;IACvCC,CAAC,EAAE,CAACH,IAAI,CAACG,CAAC,GAAGJ,GAAG,CAACI,CAAC,KAAKF,CAAC,GAAG,GAAG,CAAC,GAAGF,GAAG,CAACI,CAAC;IACvCC,CAAC,EAAE,CAACJ,IAAI,CAACI,CAAC,GAAGL,GAAG,CAACK,CAAC,KAAKH,CAAC,GAAG,GAAG,CAAC,GAAGF,GAAG,CAACK;EACxC,CAAC;AACH;AAEA,SAASC,OAAO,CAACC,OAAO,EAAEhB,EAAE,EAAE;EAC5B,IAAI,CAACgB,OAAO,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,EAAE;IACvC,OAAOG,iBAAU,CAACZ,IAAI,CAAC,IAAI,EAAE,0BAA0B,EAAEP,EAAE,CAAC;EAC9D;EAEAgB,OAAO,GAAGA,OAAO,CAACI,GAAG,CAAEC,MAAM,IAAK;IAChC,IAAIA,MAAM,CAACC,KAAK,KAAK,KAAK,IAAID,MAAM,CAACC,KAAK,KAAK,KAAK,EAAE;MACpDD,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,GAAG,IAAAC,kBAAS,EAACH,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC,CAACE,KAAK,EAAE;IACxD;IAEA,OAAOJ,MAAM;EACf,CAAC,CAAC;EAEF,IAAI,CAACpB,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAACN,MAAM,CAACO,KAAK,EAAE,IAAI,CAACP,MAAM,CAACQ,MAAM,EAAE,CAACjB,CAAC,EAAEC,CAAC,EAAEM,GAAG,KAAK;IACzE,IAAIgB,GAAG,GAAG;MACRG,CAAC,EAAE,IAAI,CAACjB,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC;MACxBoB,CAAC,EAAE,IAAI,CAAClB,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC;MAC5BqB,CAAC,EAAE,IAAI,CAACnB,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC;IAC7B,CAAC;IAED,MAAMiC,aAAa,GAAG,CAACC,CAAC,EAAEC,MAAM,KAC9B,IAAI,CAACC,WAAW,CAACC,QAAQ,CAACrB,GAAG,CAACkB,CAAC,CAAC,GAAGC,MAAM,CAAC;IAE5CZ,OAAO,CAACe,OAAO,CAAEV,MAAM,IAAK;MAC1B,IAAIA,MAAM,CAACC,KAAK,KAAK,KAAK,EAAE;QAC1Bb,GAAG,GAAGD,GAAG,CAACC,GAAG,EAAEY,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,EAAEF,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC;MACpD,CAAC,MAAM,IAAIF,MAAM,CAACC,KAAK,KAAK,MAAM,EAAE;QAClCb,GAAG,GAAGD,GAAG,CAACC,GAAG,EAAE;UAAEG,CAAC,EAAE,GAAG;UAAEC,CAAC,EAAE,GAAG;UAAEC,CAAC,EAAE;QAAI,CAAC,EAAEO,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC;MAC9D,CAAC,MAAM,IAAIF,MAAM,CAACC,KAAK,KAAK,OAAO,EAAE;QACnCb,GAAG,GAAGD,GAAG,CAACC,GAAG,EAAE;UAAEG,CAAC,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAC,EAAEO,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC;MACxD,CAAC,MAAM,IAAIF,MAAM,CAACC,KAAK,KAAK,KAAK,EAAE;QACjCb,GAAG,GAAG;UACJG,CAAC,EAAEH,GAAG,CAACG,CAAC,GAAGS,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,CAACX,CAAC;UAC7BC,CAAC,EAAEJ,GAAG,CAACI,CAAC,GAAGQ,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,CAACV,CAAC;UAC7BC,CAAC,EAAEL,GAAG,CAACK,CAAC,GAAGO,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,CAACT;QAC9B,CAAC;MACH,CAAC,MAAM,IAAIO,MAAM,CAACC,KAAK,KAAK,KAAK,EAAE;QACjCb,GAAG,CAACG,CAAC,GAAGc,aAAa,CAAC,GAAG,EAAEL,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC,MAAM,IAAIF,MAAM,CAACC,KAAK,KAAK,OAAO,EAAE;QACnCb,GAAG,CAACI,CAAC,GAAGa,aAAa,CAAC,GAAG,EAAEL,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC,MAAM,IAAIF,MAAM,CAACC,KAAK,KAAK,MAAM,EAAE;QAClCb,GAAG,CAACK,CAAC,GAAGY,aAAa,CAAC,GAAG,EAAEL,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC,MAAM;QACL,IAAIF,MAAM,CAACC,KAAK,KAAK,KAAK,EAAE;UAC1BD,MAAM,CAACC,KAAK,GAAG,MAAM;QACvB;QAEAb,GAAG,GAAG,IAAAe,kBAAS,EAACf,GAAG,CAAC;QAEpB,IAAI,CAACA,GAAG,CAACY,MAAM,CAACC,KAAK,CAAC,EAAE;UACtB,OAAOH,iBAAU,CAACZ,IAAI,CACpB,IAAI,EACJ,SAAS,GAAGc,MAAM,CAACC,KAAK,GAAG,gBAAgB,EAC3CtB,EAAE,CACH;QACH;QAEAS,GAAG,GAAGA,GAAG,CAACY,MAAM,CAACC,KAAK,CAAC,CAAC,GAAGD,MAAM,CAACE,MAAM,CAAC,CAACE,KAAK,EAAE;MACnD;IACF,CAAC,CAAC;IAEF,IAAI,CAAC9B,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,GAAGgB,GAAG,CAACG,CAAC;IAC7B,IAAI,CAACjB,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAGgB,GAAG,CAACI,CAAC;IACjC,IAAI,CAAClB,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAGgB,GAAG,CAACK,CAAC;EACnC,CAAC,CAAC;EAEF,IAAI,IAAAR,oBAAa,EAACN,EAAE,CAAC,EAAE;IACrBA,EAAE,CAACO,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC3B;EAEA,OAAO,IAAI;AACb;AAEO,MAAMyB,eAAe,GAAGC,MAAM,CAACC,MAAM,CAAC;EAC3CC,OAAO,EAAE,SAAS;EAClBC,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE,QAAQ;EAChBC,UAAU,EAAE,YAAY;EACxBC,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,WAAW;EACtBC,IAAI,EAAE,MAAM;EACZC,GAAG,EAAE,KAAK;EACVC,GAAG,EAAE,KAAK;EACVC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE,OAAO;EACdC,GAAG,EAAE,KAAK;EACVC,GAAG,EAAE,KAAK;EACVC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE;AACR,CAAC,CAAC;AAAC;AAAA,eAEY,OAAO;EACpB;AACF;AACA;AACA;AACA;AACA;EACEC,UAAU,CAACC,GAAG,EAAEnD,EAAE,EAAE;IAClB,IAAI,OAAOmD,GAAG,KAAK,QAAQ,EAAE;MAC3B,OAAOhC,iBAAU,CAACZ,IAAI,CAAC,IAAI,EAAE,qBAAqB,EAAEP,EAAE,CAAC;IACzD;IAEA,IAAImD,GAAG,GAAG,CAAC,CAAC,IAAIA,GAAG,GAAG,CAAC,CAAC,EAAE;MACxB,OAAOhC,iBAAU,CAACZ,IAAI,CACpB,IAAI,EACJ,wCAAwC,EACxCP,EAAE,CACH;IACH;IAEA,IAAI,CAACC,SAAS,CACZ,CAAC,EACD,CAAC,EACD,IAAI,CAACN,MAAM,CAACO,KAAK,EACjB,IAAI,CAACP,MAAM,CAACQ,MAAM,EAClB,UAAUjB,CAAC,EAAEC,CAAC,EAAEM,GAAG,EAAE;MACnB,IAAI0D,GAAG,GAAG,GAAG,EAAE;QACb,IAAI,CAACxD,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,IAAI,CAAC,GAAG0D,GAAG;QAChC,IAAI,CAACxD,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG0D,GAAG;QACpC,IAAI,CAACxD,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG0D,GAAG;MACtC,CAAC,MAAM;QACL,IAAI,CAACxD,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAACE,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,IAAI0D,GAAG;QAC5D,IAAI,CAACxD,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAACE,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,IAAI0D,GAAG;QACpE,IAAI,CAACxD,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAACE,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,IAAI0D,GAAG;MACtE;IACF,CAAC,CACF;IAED,IAAI,IAAA7C,oBAAa,EAACN,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACO,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACE6C,QAAQ,CAACD,GAAG,EAAEnD,EAAE,EAAE;IAChB,IAAI,OAAOmD,GAAG,KAAK,QAAQ,EAAE;MAC3B,OAAOhC,iBAAU,CAACZ,IAAI,CAAC,IAAI,EAAE,qBAAqB,EAAEP,EAAE,CAAC;IACzD;IAEA,IAAImD,GAAG,GAAG,CAAC,CAAC,IAAIA,GAAG,GAAG,CAAC,CAAC,EAAE;MACxB,OAAOhC,iBAAU,CAACZ,IAAI,CACpB,IAAI,EACJ,wCAAwC,EACxCP,EAAE,CACH;IACH;IAEA,MAAMqD,MAAM,GAAG,CAACF,GAAG,GAAG,CAAC,KAAK,CAAC,GAAGA,GAAG,CAAC;IAEpC,SAASG,MAAM,CAAClE,KAAK,EAAE;MACrBA,KAAK,GAAGmE,IAAI,CAACC,KAAK,CAACH,MAAM,IAAIjE,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;MAEhD,OAAOA,KAAK,GAAG,CAAC,GAAG,CAAC,GAAGA,KAAK,GAAG,GAAG,GAAG,GAAG,GAAGA,KAAK;IAClD;IAEA,IAAI,CAACa,SAAS,CACZ,CAAC,EACD,CAAC,EACD,IAAI,CAACN,MAAM,CAACO,KAAK,EACjB,IAAI,CAACP,MAAM,CAACQ,MAAM,EAClB,UAAUjB,CAAC,EAAEC,CAAC,EAAEM,GAAG,EAAE;MACnB,IAAI,CAACE,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,GAAG6D,MAAM,CAAC,IAAI,CAAC3D,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,CAAC;MACrD,IAAI,CAACE,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAG6D,MAAM,CAAC,IAAI,CAAC3D,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,CAAC;MAC7D,IAAI,CAACE,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAG6D,MAAM,CAAC,IAAI,CAAC3D,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,CAAC;IAC/D,CAAC,CACF;IAED,IAAI,IAAAa,oBAAa,EAACN,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACO,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEkD,SAAS,CAACC,CAAC,EAAE1D,EAAE,EAAE;IACf,IAAI,OAAO0D,CAAC,KAAK,QAAQ,EAAE;MACzB,OAAOvC,iBAAU,CAACZ,IAAI,CAAC,IAAI,EAAE,mBAAmB,EAAEP,EAAE,CAAC;IACvD;IAEA,IAAI0D,CAAC,GAAG,CAAC,EAAE;MACTA,CAAC,GAAG,CAAC;IACP,CAAC,CAAC;;IAEF,IAAI,CAACzD,SAAS,CACZ,CAAC,EACD,CAAC,EACD,IAAI,CAACN,MAAM,CAACO,KAAK,EACjB,IAAI,CAACP,MAAM,CAACQ,MAAM,EAClB,UAAUjB,CAAC,EAAEC,CAAC,EAAEM,GAAG,EAAE;MACnB,IAAI,CAACE,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,GAClB8D,IAAI,CAACC,KAAK,CAAE,IAAI,CAAC7D,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,GAAG,GAAG,IAAKiE,CAAC,GAAG,CAAC,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC,GAAI,GAAG;MACvE,IAAI,CAAC/D,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GACtB8D,IAAI,CAACC,KAAK,CAAE,IAAI,CAAC7D,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,IAAKiE,CAAC,GAAG,CAAC,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC,GAClE,GAAG;MACL,IAAI,CAAC/D,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GACtB8D,IAAI,CAACC,KAAK,CAAE,IAAI,CAAC7D,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,IAAKiE,CAAC,GAAG,CAAC,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC,GAClE,GAAG;IACP,CAAC,CACF;IAED,IAAI,IAAApD,oBAAa,EAACN,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACO,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb,CAAC;EAED;AACF;AACA;AACA;AACA;EACER,SAAS;EAET;EACA4D,SAAS,EAAE5D,SAAS;EAEpB;AACF;AACA;AACA;AACA;AACA;EACE6D,OAAO,CAACC,CAAC,EAAE7D,EAAE,EAAE;IACb,IAAI,OAAO6D,CAAC,KAAK,QAAQ,EACvB,OAAO1C,iBAAU,CAACZ,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAEP,EAAE,CAAC;IACxD,IAAI6D,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,CAAC,EAChB,OAAO1C,iBAAU,CAACZ,IAAI,CAAC,IAAI,EAAE,gCAAgC,EAAEP,EAAE,CAAC;IAEpE,IAAI,CAACC,SAAS,CACZ,CAAC,EACD,CAAC,EACD,IAAI,CAACN,MAAM,CAACO,KAAK,EACjB,IAAI,CAACP,MAAM,CAACQ,MAAM,EAClB,UAAUjB,CAAC,EAAEC,CAAC,EAAEM,GAAG,EAAE;MACnB,MAAMK,CAAC,GAAG,IAAI,CAACH,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAGoE,CAAC;MACvC,IAAI,CAAClE,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAGK,CAAC;IAC/B,CAAC,CACF;IAED,IAAI,IAAAQ,oBAAa,EAACN,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACO,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb,CAAC;EAED;AACF;AACA;AACA;AACA;EACEuD,KAAK,CAAC9D,EAAE,EAAE;IACR,IAAI,CAACC,SAAS,CACZ,CAAC,EACD,CAAC,EACD,IAAI,CAACN,MAAM,CAACO,KAAK,EACjB,IAAI,CAACP,MAAM,CAACQ,MAAM,EAClB,UAAUjB,CAAC,EAAEC,CAAC,EAAEM,GAAG,EAAE;MACnB,IAAIsE,GAAG,GAAG,IAAI,CAACpE,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC;MAC/B,IAAIuE,KAAK,GAAG,IAAI,CAACrE,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC;MACrC,IAAIwE,IAAI,GAAG,IAAI,CAACtE,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC;MAEpCsE,GAAG,GAAGA,GAAG,GAAG,KAAK,GAAGC,KAAK,GAAG,KAAK,GAAGC,IAAI,GAAG,KAAK;MAChDD,KAAK,GAAGD,GAAG,GAAG,KAAK,GAAGC,KAAK,GAAG,KAAK,GAAGC,IAAI,GAAG,KAAK;MAClDA,IAAI,GAAGF,GAAG,GAAG,KAAK,GAAGC,KAAK,GAAG,KAAK,GAAGC,IAAI,GAAG,KAAK;MAEjD,IAAI,CAACtE,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,GAAGsE,GAAG,GAAG,GAAG,GAAGA,GAAG,GAAG,GAAG;MAC7C,IAAI,CAACpE,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAGuE,KAAK,GAAG,GAAG,GAAGA,KAAK,GAAG,GAAG;MACrD,IAAI,CAACrE,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAGwE,IAAI,GAAG,GAAG,GAAGA,IAAI,GAAG,GAAG;IACrD,CAAC,CACF;IAED,IAAI,IAAA3D,oBAAa,EAACN,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACO,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACE2D,IAAI,CAACL,CAAC,EAAE7D,EAAE,EAAE;IACV,IAAI,OAAO6D,CAAC,KAAK,QAAQ,EAAE;MACzB,OAAO1C,iBAAU,CAACZ,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAEP,EAAE,CAAC;IACxD;IAEA,IAAI6D,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,CAAC,EAAE;MAClB,OAAO1C,iBAAU,CAACZ,IAAI,CAAC,IAAI,EAAE,gCAAgC,EAAEP,EAAE,CAAC;IACpE;;IAEA;IACA,IAAI,CAAC4D,OAAO,CAAC,CAAC,GAAGC,CAAC,CAAC;IAEnB,IAAI,IAAAvD,oBAAa,EAACN,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACO,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACE4D,WAAW,CAAClF,MAAM,EAAEmF,YAAY,EAAEpE,EAAE,EAAE;IACpC,IAAI,OAAOoE,YAAY,KAAK,UAAU,IAAI,OAAOpE,EAAE,KAAK,WAAW,EAAE;MACnEA,EAAE,GAAGoE,YAAY;MACjBA,YAAY,GAAG,IAAI;IACrB;IAEA,IAAI,CAACA,YAAY,EAAE;MACjBA,YAAY,GAAG,IAAI,CAACvC,WAAW,CAACwC,WAAW;IAC7C;IAEA,MAAMC,OAAO,GAAGC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC7E,MAAM,CAACC,IAAI,CAAC;IAC7C,MAAM6E,KAAK,GAAGxF,MAAM,CAACK,MAAM;IAC3B,MAAMoF,KAAK,GAAGzF,MAAM,CAAC,CAAC,CAAC,CAACK,MAAM;IAC9B,MAAMqF,MAAM,GAAGpB,IAAI,CAACC,KAAK,CAACiB,KAAK,GAAG,CAAC,CAAC;IACpC,MAAMG,MAAM,GAAGrB,IAAI,CAACC,KAAK,CAACkB,KAAK,GAAG,CAAC,CAAC;IACpC,MAAMG,MAAM,GAAG,CAACF,MAAM;IACtB,MAAMG,MAAM,GAAG,CAACF,MAAM;IAEtB,IAAIG,MAAM;IACV,IAAIC,IAAI;IACR,IAAIC,IAAI;IACR,IAAIC,IAAI;IACR,IAAIC,EAAE;IACN,IAAIC,EAAE;IACN,IAAIC,EAAE;IACN,IAAIC,EAAE;IACN,IAAIC,EAAE;IACN,IAAIC,IAAI;IAER,IAAI,CAACvF,SAAS,CACZ,CAAC,EACD,CAAC,EACD,IAAI,CAACN,MAAM,CAACO,KAAK,EACjB,IAAI,CAACP,MAAM,CAACQ,MAAM,EAClB,UAAUjB,CAAC,EAAEC,CAAC,EAAEM,GAAG,EAAE;MACnByF,IAAI,GAAG,CAAC;MACRD,IAAI,GAAG,CAAC;MACRD,IAAI,GAAG,CAAC;MAER,KAAK,IAAIS,GAAG,GAAGZ,MAAM,EAAEY,GAAG,IAAId,MAAM,EAAEc,GAAG,EAAE,EAAE;QAC3C,KAAK,IAAIC,GAAG,GAAGZ,MAAM,EAAEY,GAAG,IAAId,MAAM,EAAEc,GAAG,EAAE,EAAE;UAC3CJ,EAAE,GAAGpG,CAAC,GAAGwG,GAAG;UACZH,EAAE,GAAGpG,CAAC,GAAGsG,GAAG;UACZV,MAAM,GAAG9F,MAAM,CAACwG,GAAG,GAAGd,MAAM,CAAC,CAACe,GAAG,GAAGd,MAAM,CAAC;UAC3CY,IAAI,GAAG,IAAI,CAAC9F,aAAa,CAAC4F,EAAE,EAAEC,EAAE,EAAEnB,YAAY,CAAC;UAE/C,IAAIoB,IAAI,KAAK,CAAC,CAAC,EAAE;YACfH,EAAE,GAAG,CAAC;YACND,EAAE,GAAG,CAAC;YACND,EAAE,GAAG,CAAC;UACR,CAAC,MAAM;YACLA,EAAE,GAAG,IAAI,CAACxF,MAAM,CAACC,IAAI,CAAC4F,IAAI,GAAG,CAAC,CAAC;YAC/BJ,EAAE,GAAG,IAAI,CAACzF,MAAM,CAACC,IAAI,CAAC4F,IAAI,GAAG,CAAC,CAAC;YAC/BH,EAAE,GAAG,IAAI,CAAC1F,MAAM,CAACC,IAAI,CAAC4F,IAAI,GAAG,CAAC,CAAC;UACjC;UAEAR,IAAI,IAAID,MAAM,GAAGI,EAAE;UACnBF,IAAI,IAAIF,MAAM,GAAGK,EAAE;UACnBF,IAAI,IAAIH,MAAM,GAAGM,EAAE;QACrB;MACF;MAEA,IAAIL,IAAI,GAAG,CAAC,EAAE;QACZA,IAAI,GAAG,CAAC;MACV;MAEA,IAAIC,IAAI,GAAG,CAAC,EAAE;QACZA,IAAI,GAAG,CAAC;MACV;MAEA,IAAIC,IAAI,GAAG,CAAC,EAAE;QACZA,IAAI,GAAG,CAAC;MACV;MAEA,IAAIF,IAAI,GAAG,GAAG,EAAE;QACdA,IAAI,GAAG,GAAG;MACZ;MAEA,IAAIC,IAAI,GAAG,GAAG,EAAE;QACdA,IAAI,GAAG,GAAG;MACZ;MAEA,IAAIC,IAAI,GAAG,GAAG,EAAE;QACdA,IAAI,GAAG,GAAG;MACZ;MAEAZ,OAAO,CAAC7E,GAAG,GAAG,CAAC,CAAC,GAAGuF,IAAI;MACvBV,OAAO,CAAC7E,GAAG,GAAG,CAAC,CAAC,GAAGwF,IAAI;MACvBX,OAAO,CAAC7E,GAAG,GAAG,CAAC,CAAC,GAAGyF,IAAI;IACzB,CAAC,CACF;IAED,IAAI,CAACvF,MAAM,CAACC,IAAI,GAAG0E,OAAO;IAE1B,IAAI,IAAAhE,oBAAa,EAACN,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACO,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb,CAAC;EAED;AACF;AACA;AACA;AACA;EACEoF,MAAM,CAAC3F,EAAE,EAAE;IACT,IAAI,CAACC,SAAS,CACZ,CAAC,EACD,CAAC,EACD,IAAI,CAACN,MAAM,CAACO,KAAK,EACjB,IAAI,CAACP,MAAM,CAACQ,MAAM,EAClB,UAAUjB,CAAC,EAAEC,CAAC,EAAEM,GAAG,EAAE;MACnB,IAAI,CAACE,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG;IACjC,CAAC,CACF;IAED,IAAI,IAAAa,oBAAa,EAACN,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACO,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEqF,QAAQ,CAACvG,IAAI,EAAEH,CAAC,EAAEC,CAAC,EAAE0G,CAAC,EAAEC,CAAC,EAAE9F,EAAE,EAAE;IAC7B,IAAI,OAAOd,CAAC,KAAK,UAAU,EAAE;MAC3Bc,EAAE,GAAGd,CAAC;MACN4G,CAAC,GAAG,IAAI;MACRD,CAAC,GAAG,IAAI;MACR1G,CAAC,GAAG,IAAI;MACRD,CAAC,GAAG,IAAI;IACV,CAAC,MAAM;MACL,IAAI,OAAOG,IAAI,KAAK,QAAQ,EAAE;QAC5B,OAAO8B,iBAAU,CAACZ,IAAI,CAAC,IAAI,EAAE,uBAAuB,EAAEP,EAAE,CAAC;MAC3D;MAEA,IAAIH,KAAK,CAACX,CAAC,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;QACrC,OAAOiC,iBAAU,CAACZ,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAEP,EAAE,CAAC;MACxD;MAEA,IAAIH,KAAK,CAACV,CAAC,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;QACrC,OAAOgC,iBAAU,CAACZ,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAEP,EAAE,CAAC;MACxD;MAEA,IAAIH,KAAK,CAACgG,CAAC,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;QACrC,OAAO1E,iBAAU,CAACZ,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAEP,EAAE,CAAC;MACxD;MAEA,IAAIH,KAAK,CAACiG,CAAC,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;QACrC,OAAO3E,iBAAU,CAACZ,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAEP,EAAE,CAAC;MACxD;IACF;IAEA,MAAMf,MAAM,GAAG,CACb,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,EACxB,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,EACxB,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,CACzB;IAEDC,CAAC,GAAGA,CAAC,IAAI,CAAC;IACVC,CAAC,GAAGA,CAAC,IAAI,CAAC;IACV0G,CAAC,GAAGhG,KAAK,CAACgG,CAAC,CAAC,GAAGA,CAAC,GAAG,IAAI,CAAClG,MAAM,CAACO,KAAK,GAAGhB,CAAC;IACxC4G,CAAC,GAAGjG,KAAK,CAACiG,CAAC,CAAC,GAAGA,CAAC,GAAG,IAAI,CAACnG,MAAM,CAACQ,MAAM,GAAGhB,CAAC;IAEzC,MAAM4G,MAAM,GAAG,IAAI,CAACC,UAAU,EAAE;IAEhC,IAAI,CAAC/F,SAAS,CAACf,CAAC,EAAEC,CAAC,EAAE0G,CAAC,EAAEC,CAAC,EAAE,UAAUG,EAAE,EAAEC,EAAE,EAAEzG,GAAG,EAAE;MAChDwG,EAAE,GAAG5G,IAAI,GAAGkE,IAAI,CAACC,KAAK,CAACyC,EAAE,GAAG5G,IAAI,CAAC;MACjC6G,EAAE,GAAG7G,IAAI,GAAGkE,IAAI,CAACC,KAAK,CAAC0C,EAAE,GAAG7G,IAAI,CAAC;MAEjC,MAAMD,KAAK,GAAGL,WAAW,CAACgH,MAAM,EAAE9G,MAAM,EAAEgH,EAAE,EAAEC,EAAE,CAAC;MAEjD,IAAI,CAACvG,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,GAAGL,KAAK,CAAC,CAAC,CAAC;MAChC,IAAI,CAACO,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAGL,KAAK,CAAC,CAAC,CAAC;MACpC,IAAI,CAACO,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAGL,KAAK,CAAC,CAAC,CAAC;IACtC,CAAC,CAAC;IAEF,IAAI,IAAAkB,oBAAa,EAACN,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACO,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE4F,SAAS,CAAClH,MAAM,EAAEC,CAAC,EAAEC,CAAC,EAAE0G,CAAC,EAAEC,CAAC,EAAE9F,EAAE,EAAE;IAChC,IAAI,CAACiB,KAAK,CAACC,OAAO,CAACjC,MAAM,CAAC,EACxB,OAAOkC,iBAAU,CAACZ,IAAI,CAAC,IAAI,EAAE,6BAA6B,EAAEP,EAAE,CAAC;IAEjE,IAAI,OAAOd,CAAC,KAAK,UAAU,EAAE;MAC3Bc,EAAE,GAAGd,CAAC;MACNA,CAAC,GAAG,IAAI;MACRC,CAAC,GAAG,IAAI;MACR0G,CAAC,GAAG,IAAI;MACRC,CAAC,GAAG,IAAI;IACV,CAAC,MAAM;MACL,IAAIjG,KAAK,CAACX,CAAC,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;QACrC,OAAOiC,iBAAU,CAACZ,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAEP,EAAE,CAAC;MACxD;MAEA,IAAIH,KAAK,CAACV,CAAC,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;QACrC,OAAOgC,iBAAU,CAACZ,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAEP,EAAE,CAAC;MACxD;MAEA,IAAIH,KAAK,CAACgG,CAAC,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;QACrC,OAAO1E,iBAAU,CAACZ,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAEP,EAAE,CAAC;MACxD;MAEA,IAAIH,KAAK,CAACiG,CAAC,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;QACrC,OAAO3E,iBAAU,CAACZ,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAEP,EAAE,CAAC;MACxD;IACF;IAEAd,CAAC,GAAGW,KAAK,CAACX,CAAC,CAAC,GAAGA,CAAC,GAAG,CAAC;IACpBC,CAAC,GAAGU,KAAK,CAACV,CAAC,CAAC,GAAGA,CAAC,GAAG,CAAC;IACpB0G,CAAC,GAAGhG,KAAK,CAACgG,CAAC,CAAC,GAAGA,CAAC,GAAG,IAAI,CAAClG,MAAM,CAACO,KAAK,GAAGhB,CAAC;IACxC4G,CAAC,GAAGjG,KAAK,CAACiG,CAAC,CAAC,GAAGA,CAAC,GAAG,IAAI,CAACnG,MAAM,CAACQ,MAAM,GAAGhB,CAAC;IAEzC,MAAM4G,MAAM,GAAG,IAAI,CAACC,UAAU,EAAE;IAEhC,IAAI,CAAC/F,SAAS,CAACf,CAAC,EAAEC,CAAC,EAAE0G,CAAC,EAAEC,CAAC,EAAE,UAAUG,EAAE,EAAEC,EAAE,EAAEzG,GAAG,EAAE;MAChD,MAAML,KAAK,GAAGL,WAAW,CAACgH,MAAM,EAAE9G,MAAM,EAAEgH,EAAE,EAAEC,EAAE,CAAC;MAEjD,IAAI,CAACvG,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,GAAG,IAAI,CAACoC,WAAW,CAACC,QAAQ,CAAC1C,KAAK,CAAC,CAAC,CAAC,CAAC;MAC3D,IAAI,CAACO,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAACoC,WAAW,CAACC,QAAQ,CAAC1C,KAAK,CAAC,CAAC,CAAC,CAAC;MAC/D,IAAI,CAACO,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAACoC,WAAW,CAACC,QAAQ,CAAC1C,KAAK,CAAC,CAAC,CAAC,CAAC;IACjE,CAAC,CAAC;IAEF,IAAI,IAAAkB,oBAAa,EAACN,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACO,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACE6F,KAAK,EAAErF,OAAO;EACdsF,MAAM,EAAEtF;AACV,CAAC,CAAC;AAAA"}