{"version": 3, "file": "constants.js", "names": ["AUTO", "HORIZONTAL_ALIGN_LEFT", "HORIZONTAL_ALIGN_CENTER", "HORIZONTAL_ALIGN_RIGHT", "VERTICAL_ALIGN_TOP", "VERTICAL_ALIGN_MIDDLE", "VERTICAL_ALIGN_BOTTOM", "BLEND_SOURCE_OVER", "BLEND_DESTINATION_OVER", "BLEND_MULTIPLY", "BLEND_ADD", "BLEND_SCREEN", "BLEND_OVERLAY", "BLEND_DARKEN", "BLEND_LIGHTEN", "BLEND_HARDLIGHT", "BLEND_DIFFERENCE", "BLEND_EXCLUSION", "EDGE_EXTEND", "EDGE_WRAP", "EDGE_CROP"], "sources": ["../src/constants.js"], "sourcesContent": ["// used to auto resizing etc.\nexport const AUTO = -1;\n\n// Align modes for cover, contain, bit masks\nexport const HORIZONTAL_ALIGN_LEFT = 1;\nexport const HORIZONTAL_ALIGN_CENTER = 2;\nexport const HORIZONTAL_ALIGN_RIGHT = 4;\n\nexport const VERTICAL_ALIGN_TOP = 8;\nexport const VERTICAL_ALIGN_MIDDLE = 16;\nexport const VERTICAL_ALIGN_BOTTOM = 32;\n\n// blend modes\nexport const BLEND_SOURCE_OVER = \"srcOver\";\nexport const BLEND_DESTINATION_OVER = \"dstOver\";\nexport const BLEND_MULTIPLY = \"multiply\";\nexport const BLEND_ADD = \"add\";\nexport const BLEND_SCREEN = \"screen\";\nexport const BLEND_OVERLAY = \"overlay\";\nexport const BLEND_DARKEN = \"darken\";\nexport const BLEND_LIGHTEN = \"lighten\";\nexport const BLEND_HARDLIGHT = \"hardLight\";\nexport const BLEND_DIFFERENCE = \"difference\";\nexport const BLEND_EXCLUSION = \"exclusion\";\n\n// Edge Handling\nexport const EDGE_EXTEND = 1;\nexport const EDGE_WRAP = 2;\nexport const EDGE_CROP = 3;\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,IAAI,GAAG,CAAC,CAAC;;AAEtB;AACA,OAAO,MAAMC,qBAAqB,GAAG,CAAC;AACtC,OAAO,MAAMC,uBAAuB,GAAG,CAAC;AACxC,OAAO,MAAMC,sBAAsB,GAAG,CAAC;AAEvC,OAAO,MAAMC,kBAAkB,GAAG,CAAC;AACnC,OAAO,MAAMC,qBAAqB,GAAG,EAAE;AACvC,OAAO,MAAMC,qBAAqB,GAAG,EAAE;;AAEvC;AACA,OAAO,MAAMC,iBAAiB,GAAG,SAAS;AAC1C,OAAO,MAAMC,sBAAsB,GAAG,SAAS;AAC/C,OAAO,MAAMC,cAAc,GAAG,UAAU;AACxC,OAAO,MAAMC,SAAS,GAAG,KAAK;AAC9B,OAAO,MAAMC,YAAY,GAAG,QAAQ;AACpC,OAAO,MAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,MAAMC,YAAY,GAAG,QAAQ;AACpC,OAAO,MAAMC,aAAa,GAAG,SAAS;AACtC,OAAO,MAAMC,eAAe,GAAG,WAAW;AAC1C,OAAO,MAAMC,gBAAgB,GAAG,YAAY;AAC5C,OAAO,MAAMC,eAAe,GAAG,WAAW;;AAE1C;AACA,OAAO,MAAMC,WAAW,GAAG,CAAC;AAC5B,OAAO,MAAMC,SAAS,GAAG,CAAC;AAC1B,OAAO,MAAMC,SAAS,GAAG,CAAC"}