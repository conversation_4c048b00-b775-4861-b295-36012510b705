{"version": 3, "file": "flipping.test.js", "names": ["<PERSON><PERSON>", "mkJGD", "configure", "flip", "expectToBeJGD", "jimp", "plugins", "describe", "it", "src", "read", "result", "getJGDSync"], "sources": ["../test/flipping.test.js"], "sourcesContent": ["import { <PERSON><PERSON>, mkJG<PERSON> } from \"@jimp/test-utils\";\n\nimport configure from \"@jimp/custom\";\n\nimport flip from \"../src\";\nimport { expectToBeJGD } from \"@jimp/test-utils/src\";\n\nconst jimp = configure({ plugins: [flip] }, Jimp);\n\ndescribe(\"Flipping plugin\", () => {\n  it(\"can flip horizontally\", async () => {\n    const src = await jimp.read(\n      mkJGD(\n        \"AAAABBBB\",\n        \"AAABAAAB\",\n        \"ABABABAB\",\n        \"CCCCCCCC\",\n        \"CCCCCCCC\",\n        \"CCCCCCCC\",\n        \"AACCCCAA\"\n      )\n    );\n\n    const result = src.flip(true, false);\n\n    expectToBeJGD(\n      result.getJGDSync(),\n      mkJGD(\n        \"BBBBAAAA\",\n        \"BAAABAAA\",\n        \"BABABABA\",\n        \"CCCCCCCC\",\n        \"CCCCCCCC\",\n        \"CCCCCCCC\",\n        \"AACCCCAA\"\n      )\n    );\n  });\n\n  it(\"can flip vertically\", async () => {\n    const src = await jimp.read(\n      mkJGD(\n        \"AAAABBBB\",\n        \"AAABAAAB\",\n        \"ABABABAB\",\n        \"CCCCCCCC\",\n        \"CCCCCCCC\",\n        \"CCCCCCCC\",\n        \"AACCCCAA\"\n      )\n    );\n\n    const result = src.flip(false, true);\n\n    expectToBeJGD(\n      result.getJGDSync(),\n      mkJGD(\n        \"AACCCCAA\",\n        \"CCCCCCCC\",\n        \"CCCCCCCC\",\n        \"CCCCCCCC\",\n        \"ABABABAB\",\n        \"AAABAAAB\",\n        \"AAAABBBB\"\n      )\n    );\n  });\n\n  it(\"can flip both horizontally and vertically at once\", async () => {\n    const src = await jimp.read(\n      mkJGD(\n        \"AAAABBBB\",\n        \"AAABAAAB\",\n        \"ABABABAB\",\n        \"CCCCCCCC\",\n        \"CCCCCCCC\",\n        \"CCCCCCCC\",\n        \"AACCCCAA\"\n      )\n    );\n\n    const result = src.flip(true, true);\n\n    expectToBeJGD(\n      result.getJGDSync(),\n      mkJGD(\n        \"AACCCCAA\",\n        \"CCCCCCCC\",\n        \"CCCCCCCC\",\n        \"CCCCCCCC\",\n        \"BABABABA\",\n        \"BAAABAAA\",\n        \"BBBBAAAA\"\n      )\n    );\n  });\n});\n"], "mappings": "AAAA,SAASA,IAAI,EAAEC,KAAK,QAAQ,kBAAkB;AAE9C,OAAOC,SAAS,MAAM,cAAc;AAEpC,OAAOC,IAAI,MAAM,QAAQ;AACzB,SAASC,aAAa,QAAQ,sBAAsB;AAEpD,MAAMC,IAAI,GAAGH,SAAS,CAAC;EAAEI,OAAO,EAAE,CAACH,IAAI;AAAE,CAAC,EAAEH,IAAI,CAAC;AAEjDO,QAAQ,CAAC,iBAAiB,EAAE,MAAM;EAChCC,EAAE,CAAC,uBAAuB,EAAE,YAAY;IACtC,MAAMC,GAAG,GAAG,MAAMJ,IAAI,CAACK,IAAI,CACzBT,KAAK,CACH,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,CACX,CACF;IAED,MAAMU,MAAM,GAAGF,GAAG,CAACN,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC;IAEpCC,aAAa,CACXO,MAAM,CAACC,UAAU,EAAE,EACnBX,KAAK,CACH,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,CACX,CACF;EACH,CAAC,CAAC;EAEFO,EAAE,CAAC,qBAAqB,EAAE,YAAY;IACpC,MAAMC,GAAG,GAAG,MAAMJ,IAAI,CAACK,IAAI,CACzBT,KAAK,CACH,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,CACX,CACF;IAED,MAAMU,MAAM,GAAGF,GAAG,CAACN,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC;IAEpCC,aAAa,CACXO,MAAM,CAACC,UAAU,EAAE,EACnBX,KAAK,CACH,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,CACX,CACF;EACH,CAAC,CAAC;EAEFO,EAAE,CAAC,mDAAmD,EAAE,YAAY;IAClE,MAAMC,GAAG,GAAG,MAAMJ,IAAI,CAACK,IAAI,CACzBT,KAAK,CACH,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,CACX,CACF;IAED,MAAMU,MAAM,GAAGF,GAAG,CAACN,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;IAEnCC,aAAa,CACXO,MAAM,CAACC,UAAU,EAAE,EACnBX,KAAK,CACH,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,CACX,CACF;EACH,CAAC,CAAC;AACJ,CAAC,CAAC"}