{"name": "image-to-pdf-converter", "version": "2.0.0", "description": "高性能图片转PDF转换器 - Electron版本", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "dist": "npm run build-win", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps"}, "keywords": ["image", "pdf", "converter", "electron", "desktop"], "author": "zz (TTM自控工程师)", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"pdf-lib": "^1.17.1", "jimp": "^0.22.10", "crypto-js": "^4.2.0", "node-machine-id": "^1.1.12", "axios": "^1.6.0"}, "build": {"appId": "com.ttm.image-to-pdf", "productName": "图片转PDF工具", "directories": {"output": "dist"}, "files": ["src/**/*", "node_modules/**/*", "package.json"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "requestedExecutionLevel": "asInvoker", "sign": null}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "图片转PDF工具"}, "extraResources": [{"from": "assets/", "to": "assets/", "filter": ["**/*"]}]}}