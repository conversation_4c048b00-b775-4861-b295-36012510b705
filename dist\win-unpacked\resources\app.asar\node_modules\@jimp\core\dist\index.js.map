{"version": 3, "file": "index.js", "names": ["alphabet", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "NaN", "i", "maxHash", "anyBase", "BIN", "slice", "Array", "join", "push", "length", "noop", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test", "Object", "prototype", "toString", "call", "toLowerCase", "indexOf", "bufferFromArrayBuffer", "arrayBuffer", "buffer", "<PERSON><PERSON><PERSON>", "alloc", "byteLength", "view", "Uint8Array", "loadFromURL", "options", "cb", "request", "err", "data", "<PERSON><PERSON><PERSON><PERSON>", "Error", "url", "loadBufferFromPath", "src", "fs", "readFile", "match", "isRawRGBAData", "obj", "width", "height", "Uint8ClampedArray", "makeRGBABufferFromRGB", "rgbaBuffer", "allocUnsafe", "j", "emptyBitmap", "<PERSON><PERSON>", "EventEmitter", "constructor", "args", "MIME_PNG", "path", "promisify", "write", "mime", "getBase64", "<PERSON><PERSON><PERSON><PERSON>", "getBufferAsync", "getPixelColor", "setPixelColor", "jimpInstance", "finish", "evData", "methodName", "setTimeout", "emitError", "emitMulti", "parseInt", "w", "h", "_background", "cssColorToHex", "throwError", "bitmap", "writeUInt32BE", "parseBitmap", "original", "from", "_quality", "_deflateLevel", "_deflateStrategy", "_filterType", "_rgba", "_originalMime", "imageData", "isRGBA", "extraConstructor", "__extraConstructors", "find", "c", "Promise", "resolve", "reject", "run", "then", "catch", "rgba", "bool", "isNodePattern", "eventName", "assign", "emit", "getHeight", "getWidth", "inspect", "getMIME", "getExtension", "MIME", "createWriteStream", "getType", "pathObj", "Path", "parse", "dir", "mkdirSync", "recursive", "stream", "on", "end", "AUTO", "hash", "base", "pHash", "ImagePHash", "getHash", "distanceFromHash", "compareHash", "currentHash", "distance", "getPixelIndex", "x", "y", "edgeHandling", "xi", "yi", "EDGE_EXTEND", "Math", "round", "EDGE_WRAP", "idx", "hex", "readUInt32BE", "has<PERSON><PERSON><PERSON>", "byteLen", "scanIterator", "addConstants", "constants", "entries", "for<PERSON>ach", "name", "value", "addJimpMethods", "methods", "composite", "appendConstructorOption", "read", "image", "create", "rgbaToInt", "r", "g", "b", "a", "intToRGBA", "floor", "pow", "cssColor", "Number", "tinyColor", "toHex8", "limit255", "n", "max", "min", "diff", "img1", "img2", "threshold", "bmp1", "bmp2", "clone<PERSON>uiet", "resize", "numDiffPixels", "pixelMatch", "percent", "phash", "hash1", "hash2", "compareHashes", "colorDiff", "rgba1", "rgba2", "maxVal", "jimpEvMethod", "evName", "method", "evNameBefore", "evNameAfter", "replace", "wrappedCb", "apply", "result", "error", "clone", "jimp<PERSON>v<PERSON><PERSON><PERSON>", "f", "scan", "process", "env", "ENVIRONMENT", "gl", "window", "self"], "sources": ["../src/index.js"], "sourcesContent": ["import fs from \"fs\";\nimport Path from \"path\";\nimport EventEmitter from \"events\";\n\nimport { isNodePattern, throwError, scan, scanIterator } from \"@jimp/utils\";\nimport anyBase from \"any-base\";\nimport pixelMatch from \"pixelmatch\";\nimport tinyColor from \"tinycolor2\";\n\nimport ImagePHash from \"./modules/phash\";\nimport request from \"./request\";\n\nimport composite from \"./composite\";\nimport promisify from \"./utils/promisify\";\nimport * as MIME from \"./utils/mime\";\nimport { parseBitmap, getBuffer, getBufferAsync } from \"./utils/image-bitmap\";\nimport * as constants from \"./constants\";\n\nconst alphabet =\n  \"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ$_\";\n\n// an array storing the maximum string length of hashes at various bases\n// 0 and 1 do not exist as possible hash lengths\nconst maxHashLength = [NaN, NaN];\n\nfor (let i = 2; i < 65; i++) {\n  const maxHash = anyBase(\n    anyBase.BIN,\n    alphabet.slice(0, i)\n  )(new Array(64 + 1).join(\"1\"));\n  maxHashLength.push(maxHash.length);\n}\n\n// no operation\nfunction noop() {}\n\n// error checking methods\n\nfunction isArrayBuffer(test) {\n  return (\n    Object.prototype.toString.call(test).toLowerCase().indexOf(\"arraybuffer\") >\n    -1\n  );\n}\n\n// Prepare a Buffer object from the arrayBuffer. Necessary in the browser > node conversion,\n// But this function is not useful when running in node directly\nfunction bufferFromArrayBuffer(arrayBuffer) {\n  const buffer = Buffer.alloc(arrayBuffer.byteLength);\n  const view = new Uint8Array(arrayBuffer);\n\n  for (let i = 0; i < buffer.length; ++i) {\n    buffer[i] = view[i];\n  }\n\n  return buffer;\n}\n\nfunction loadFromURL(options, cb) {\n  request(options, (err, data) => {\n    if (err) {\n      return cb(err);\n    }\n\n    if (typeof data === \"object\" && Buffer.isBuffer(data)) {\n      return cb(null, data);\n    }\n\n    if (typeof data === \"object\" && isArrayBuffer(data)) {\n      return cb(null, bufferFromArrayBuffer(data));\n    }\n\n    return new Error(`Could not load Buffer from <${options.url}>`);\n  });\n}\n\nfunction loadBufferFromPath(src, cb) {\n  if (\n    fs &&\n    typeof fs.readFile === \"function\" &&\n    !src.match(/^(http|ftp)s?:\\/\\/./)\n  ) {\n    fs.readFile(src, cb);\n  } else {\n    loadFromURL({ url: src }, cb);\n  }\n}\n\nfunction isRawRGBAData(obj) {\n  return (\n    obj &&\n    typeof obj === \"object\" &&\n    typeof obj.width === \"number\" &&\n    typeof obj.height === \"number\" &&\n    (Buffer.isBuffer(obj.data) ||\n      obj.data instanceof Uint8Array ||\n      (typeof Uint8ClampedArray === \"function\" &&\n        obj.data instanceof Uint8ClampedArray)) &&\n    (obj.data.length === obj.width * obj.height * 4 ||\n      obj.data.length === obj.width * obj.height * 3)\n  );\n}\n\nfunction makeRGBABufferFromRGB(buffer) {\n  if (buffer.length % 3 !== 0) {\n    throw new Error(\"Buffer length is incorrect\");\n  }\n\n  const rgbaBuffer = Buffer.allocUnsafe((buffer.length / 3) * 4);\n  let j = 0;\n\n  for (let i = 0; i < buffer.length; i++) {\n    rgbaBuffer[j] = buffer[i];\n\n    if ((i + 1) % 3 === 0) {\n      rgbaBuffer[++j] = 255;\n    }\n\n    j++;\n  }\n\n  return rgbaBuffer;\n}\n\nconst emptyBitmap = {\n  data: null,\n  width: null,\n  height: null,\n};\n\n/**\n * Jimp constructor (from a file)\n * @param path a path to the image\n * @param {function(Error, Jimp)} cb (optional) a function to call when the image is parsed to a bitmap\n */\n\n/**\n * Jimp constructor (from a url with options)\n * @param options { url, otherOptions}\n * @param {function(Error, Jimp)} cb (optional) a function to call when the image is parsed to a bitmap\n */\n\n/**\n * Jimp constructor (from another Jimp image or raw image data)\n * @param image a Jimp image to clone\n * @param {function(Error, Jimp)} cb a function to call when the image is parsed to a bitmap\n */\n\n/**\n * Jimp constructor (from a Buffer)\n * @param data a Buffer containing the image data\n * @param {function(Error, Jimp)} cb a function to call when the image is parsed to a bitmap\n */\n\n/**\n * Jimp constructor (to generate a new image)\n * @param w the width of the image\n * @param h the height of the image\n * @param {function(Error, Jimp)} cb (optional) a function to call when the image is parsed to a bitmap\n */\n\n/**\n * Jimp constructor (to generate a new image)\n * @param w the width of the image\n * @param h the height of the image\n * @param background color to fill the image with\n * @param {function(Error, Jimp)} cb (optional) a function to call when the image is parsed to a bitmap\n */\n\nclass Jimp extends EventEmitter {\n  // An object representing a bitmap in memory, comprising:\n  //  - data: a buffer of the bitmap data\n  //  - width: the width of the image in pixels\n  //  - height: the height of the image in pixels\n  bitmap = emptyBitmap;\n\n  // Default colour to use for new pixels\n  _background = 0x00000000;\n\n  // Default MIME is PNG\n  _originalMime = Jimp.MIME_PNG;\n\n  // Exif data for the image\n  _exif = null;\n\n  // Whether Transparency supporting formats will be exported as RGB or RGBA\n  _rgba = true;\n\n  constructor(...args) {\n    super();\n\n    const jimpInstance = this;\n    let cb = noop;\n\n    if (isArrayBuffer(args[0])) {\n      args[0] = bufferFromArrayBuffer(args[0]);\n    }\n\n    function finish(...args) {\n      const [err] = args;\n      const evData = err || {};\n      evData.methodName = \"constructor\";\n\n      setTimeout(() => {\n        // run on next tick.\n        if (err && cb === noop) {\n          jimpInstance.emitError(\"constructor\", err);\n        } else if (!err) {\n          jimpInstance.emitMulti(\"constructor\", \"initialized\");\n        }\n\n        cb.call(jimpInstance, ...args);\n      }, 1);\n    }\n\n    if (\n      (typeof args[0] === \"number\" && typeof args[1] === \"number\") ||\n      (parseInt(args[0], 10) && parseInt(args[1], 10))\n    ) {\n      // create a new image\n      const w = parseInt(args[0], 10);\n      const h = parseInt(args[1], 10);\n      cb = args[2];\n\n      // with a hex color\n      if (typeof args[2] === \"number\") {\n        this._background = args[2];\n        cb = args[3];\n      }\n\n      // with a css color\n      if (typeof args[2] === \"string\") {\n        this._background = Jimp.cssColorToHex(args[2]);\n        cb = args[3];\n      }\n\n      if (typeof cb === \"undefined\") {\n        cb = noop;\n      }\n\n      if (typeof cb !== \"function\") {\n        return throwError.call(this, \"cb must be a function\", finish);\n      }\n\n      this.bitmap = {\n        data: Buffer.alloc(w * h * 4),\n        width: w,\n        height: h,\n      };\n\n      for (let i = 0; i < this.bitmap.data.length; i += 4) {\n        this.bitmap.data.writeUInt32BE(this._background, i);\n      }\n\n      finish(null, this);\n    } else if (typeof args[0] === \"object\" && args[0].url) {\n      cb = args[1] || noop;\n\n      if (typeof cb !== \"function\") {\n        return throwError.call(this, \"cb must be a function\", finish);\n      }\n\n      loadFromURL(args[0], (err, data) => {\n        if (err) {\n          return throwError.call(this, err, finish);\n        }\n\n        this.parseBitmap(data, args[0].url, finish);\n      });\n    } else if (args[0] instanceof Jimp) {\n      // clone an existing Jimp\n      const [original] = args;\n      cb = args[1];\n\n      if (typeof cb === \"undefined\") {\n        cb = noop;\n      }\n\n      if (typeof cb !== \"function\") {\n        return throwError.call(this, \"cb must be a function\", finish);\n      }\n\n      this.bitmap = {\n        data: Buffer.from(original.bitmap.data),\n        width: original.bitmap.width,\n        height: original.bitmap.height,\n      };\n\n      this._quality = original._quality;\n      this._deflateLevel = original._deflateLevel;\n      this._deflateStrategy = original._deflateStrategy;\n      this._filterType = original._filterType;\n      this._rgba = original._rgba;\n      this._background = original._background;\n      this._originalMime = original._originalMime;\n\n      finish(null, this);\n    } else if (isRawRGBAData(args[0])) {\n      const [imageData] = args;\n      cb = args[1] || noop;\n\n      const isRGBA =\n        imageData.width * imageData.height * 4 === imageData.data.length;\n      const buffer = isRGBA\n        ? Buffer.from(imageData.data)\n        : makeRGBABufferFromRGB(imageData.data);\n\n      this.bitmap = {\n        data: buffer,\n        width: imageData.width,\n        height: imageData.height,\n      };\n\n      finish(null, this);\n    } else if (typeof args[0] === \"string\") {\n      // read from a path\n      const path = args[0];\n      cb = args[1];\n\n      if (typeof cb === \"undefined\") {\n        cb = noop;\n      }\n\n      if (typeof cb !== \"function\") {\n        return throwError.call(this, \"cb must be a function\", finish);\n      }\n\n      loadBufferFromPath(path, (err, data) => {\n        if (err) {\n          return throwError.call(this, err, finish);\n        }\n\n        this.parseBitmap(data, path, finish);\n      });\n    } else if (typeof args[0] === \"object\" && Buffer.isBuffer(args[0])) {\n      // read from a buffer\n      const data = args[0];\n      cb = args[1];\n\n      if (typeof cb !== \"function\") {\n        return throwError.call(this, \"cb must be a function\", finish);\n      }\n\n      this.parseBitmap(data, null, finish);\n    } else {\n      // Allow client libs to add new ways to build a Jimp object.\n      // Extra constructors must be added by `Jimp.appendConstructorOption()`\n      cb = args[args.length - 1];\n\n      if (typeof cb !== \"function\") {\n        // TODO: try to solve the args after cb problem.\n        cb = args[args.length - 2];\n\n        if (typeof cb !== \"function\") {\n          cb = noop;\n        }\n      }\n\n      const extraConstructor = Jimp.__extraConstructors.find((c) =>\n        c.test(...args)\n      );\n\n      if (extraConstructor) {\n        new Promise((resolve, reject) => {\n          extraConstructor.run.call(this, resolve, reject, ...args);\n        })\n          .then(() => finish(null, this))\n          .catch(finish);\n      } else {\n        return throwError.call(\n          this,\n          \"No matching constructor overloading was found. \" +\n            \"Please see the docs for how to call the Jimp constructor.\",\n          finish\n        );\n      }\n    }\n  }\n\n  /**\n   * Parse a bitmap with the loaded image types.\n   *\n   * @param {Buffer} data raw image data\n   * @param {string} path optional path to file\n   * @param {function(Error, Jimp)} finish (optional) a callback for when complete\n   * @memberof Jimp\n   */\n  parseBitmap(data, path, finish) {\n    parseBitmap.call(this, data, null, finish);\n  }\n\n  /**\n   * Sets the type of the image (RGB or RGBA) when saving in a format that supports transparency (default is RGBA)\n   * @param {boolean} bool A Boolean, true to use RGBA or false to use RGB\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp} this for chaining of methods\n   */\n  rgba(bool, cb) {\n    if (typeof bool !== \"boolean\") {\n      return throwError.call(\n        this,\n        \"bool must be a boolean, true for RGBA or false for RGB\",\n        cb\n      );\n    }\n\n    this._rgba = bool;\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  }\n\n  /**\n   * Emit for multiple listeners\n   * @param {string} methodName name of the method to emit an error for\n   * @param {string} eventName name of the eventName to emit an error for\n   * @param {object} data to emit\n   */\n  emitMulti(methodName, eventName, data = {}) {\n    data = Object.assign(data, { methodName, eventName });\n    this.emit(\"any\", data);\n\n    if (methodName) {\n      this.emit(methodName, data);\n    }\n\n    this.emit(eventName, data);\n  }\n\n  emitError(methodName, err) {\n    this.emitMulti(methodName, \"error\", err);\n  }\n\n  /**\n   * Get the current height of the image\n   * @return {number} height of the image\n   */\n  getHeight() {\n    return this.bitmap.height;\n  }\n\n  /**\n   * Get the current width of the image\n   * @return {number} width of the image\n   */\n  getWidth() {\n    return this.bitmap.width;\n  }\n\n  /**\n   * Nicely format Jimp object when sent to the console e.g. console.log(image)\n   * @returns {string} pretty printed\n   */\n  inspect() {\n    return (\n      \"<Jimp \" +\n      (this.bitmap === emptyBitmap\n        ? \"pending...\"\n        : this.bitmap.width + \"x\" + this.bitmap.height) +\n      \">\"\n    );\n  }\n\n  /**\n   * Nicely format Jimp object when converted to a string\n   * @returns {string} pretty printed\n   */\n  toString() {\n    return \"[object Jimp]\";\n  }\n\n  /**\n   * Returns the original MIME of the image (default: \"image/png\")\n   * @returns {string} the MIME\n   */\n  getMIME() {\n    const mime = this._originalMime || Jimp.MIME_PNG;\n\n    return mime;\n  }\n\n  /**\n   * Returns the appropriate file extension for the original MIME of the image (default: \"png\")\n   * @returns {string} the file extension\n   */\n  getExtension() {\n    const mime = this.getMIME();\n\n    return MIME.getExtension(mime);\n  }\n\n  /**\n   * Writes the image to a file\n   * @param {string} path a path to the destination file\n   * @param {function(Error, Jimp)} cb (optional) a function to call when the image is saved to disk\n   * @returns {Jimp} this for chaining of methods\n   */\n  write(path, cb) {\n    if (!fs || !fs.createWriteStream) {\n      throw new Error(\n        \"Cant access the filesystem. You can use the getBase64 method.\"\n      );\n    }\n\n    if (typeof path !== \"string\") {\n      return throwError.call(this, \"path must be a string\", cb);\n    }\n\n    if (typeof cb === \"undefined\") {\n      cb = noop;\n    }\n\n    if (typeof cb !== \"function\") {\n      return throwError.call(this, \"cb must be a function\", cb);\n    }\n\n    const mime = MIME.getType(path) || this.getMIME();\n    const pathObj = Path.parse(path);\n\n    if (pathObj.dir) {\n      fs.mkdirSync(pathObj.dir, { recursive: true });\n    }\n\n    this.getBuffer(mime, (err, buffer) => {\n      if (err) {\n        return throwError.call(this, err, cb);\n      }\n\n      const stream = fs.createWriteStream(path);\n\n      stream\n        .on(\"open\", () => {\n          stream.write(buffer);\n          stream.end();\n        })\n        .on(\"error\", (err) => {\n          return throwError.call(this, err, cb);\n        });\n      stream.on(\"finish\", () => {\n        cb.call(this, null, this);\n      });\n    });\n\n    return this;\n  }\n\n  writeAsync = (path) => promisify(this.write, this, path);\n\n  /**\n   * Converts the image to a base 64 string\n   * @param {string} mime the mime type of the image data to be created\n   * @param {function(Error, Jimp)} cb a Node-style function to call with the buffer as the second argument\n   * @returns {Jimp} this for chaining of methods\n   */\n  getBase64(mime, cb) {\n    if (mime === Jimp.AUTO) {\n      // allow auto MIME detection\n      mime = this.getMIME();\n    }\n\n    if (typeof mime !== \"string\") {\n      return throwError.call(this, \"mime must be a string\", cb);\n    }\n\n    if (typeof cb !== \"function\") {\n      return throwError.call(this, \"cb must be a function\", cb);\n    }\n\n    this.getBuffer(mime, function (err, data) {\n      if (err) {\n        return throwError.call(this, err, cb);\n      }\n\n      const src = \"data:\" + mime + \";base64,\" + data.toString(\"base64\");\n      cb.call(this, null, src);\n    });\n\n    return this;\n  }\n\n  getBase64Async = (mime) => promisify(this.getBase64, this, mime);\n\n  /**\n   * Generates a perceptual hash of the image <https://en.wikipedia.org/wiki/Perceptual_hashing>. And pads the string. Can configure base.\n   * @param {number} base (optional) a number between 2 and 64 representing the base for the hash (e.g. 2 is binary, 10 is decimal, 16 is hex, 64 is base 64). Defaults to 64.\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {string} a string representing the hash\n   */\n  hash(base, cb) {\n    base = base || 64;\n\n    if (typeof base === \"function\") {\n      cb = base;\n      base = 64;\n    }\n\n    if (typeof base !== \"number\") {\n      return throwError.call(this, \"base must be a number\", cb);\n    }\n\n    if (base < 2 || base > 64) {\n      return throwError.call(\n        this,\n        \"base must be a number between 2 and 64\",\n        cb\n      );\n    }\n\n    let hash = this.pHash();\n    hash = anyBase(anyBase.BIN, alphabet.slice(0, base))(hash);\n\n    while (hash.length < maxHashLength[base]) {\n      hash = \"0\" + hash; // pad out with leading zeros\n    }\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, hash);\n    }\n\n    return hash;\n  }\n\n  /**\n   * Calculates the perceptual hash\n   * @returns {number} the perceptual hash\n   */\n  pHash() {\n    const pHash = new ImagePHash();\n    return pHash.getHash(this);\n  }\n\n  /**\n   * Calculates the hamming distance of the current image and a hash based on their perceptual hash\n   * @param {hash} compareHash hash to compare to\n   * @returns {number} a number ranging from 0 to 1, 0 means they are believed to be identical\n   */\n  distanceFromHash(compareHash) {\n    const pHash = new ImagePHash();\n    const currentHash = pHash.getHash(this);\n\n    return pHash.distance(currentHash, compareHash);\n  }\n\n  /**\n   * Converts the image to a buffer\n   * @param {string} mime the mime type of the image buffer to be created\n   * @param {function(Error, Jimp)} cb a Node-style function to call with the buffer as the second argument\n   * @returns {Jimp} this for chaining of methods\n   */\n  getBuffer = getBuffer;\n\n  getBufferAsync = getBufferAsync;\n\n  /**\n   * Returns the offset of a pixel in the bitmap buffer\n   * @param {number} x the x coordinate\n   * @param {number} y the y coordinate\n   * @param {number} edgeHandling (optional) define how to sum pixels from outside the border\n   * @param {number} cb (optional) a callback for when complete\n   * @returns {number} the index of the pixel or -1 if not found\n   */\n  getPixelIndex(x, y, edgeHandling, cb) {\n    let xi;\n    let yi;\n\n    if (typeof edgeHandling === \"function\" && typeof cb === \"undefined\") {\n      cb = edgeHandling;\n      edgeHandling = null;\n    }\n\n    if (!edgeHandling) {\n      edgeHandling = Jimp.EDGE_EXTEND;\n    }\n\n    if (typeof x !== \"number\" || typeof y !== \"number\") {\n      return throwError.call(this, \"x and y must be numbers\", cb);\n    }\n\n    // round input\n    x = Math.round(x);\n    y = Math.round(y);\n    xi = x;\n    yi = y;\n\n    if (edgeHandling === Jimp.EDGE_EXTEND) {\n      if (x < 0) xi = 0;\n      if (x >= this.bitmap.width) xi = this.bitmap.width - 1;\n      if (y < 0) yi = 0;\n      if (y >= this.bitmap.height) yi = this.bitmap.height - 1;\n    }\n\n    if (edgeHandling === Jimp.EDGE_WRAP) {\n      if (x < 0) {\n        xi = this.bitmap.width + x;\n      }\n\n      if (x >= this.bitmap.width) {\n        xi = x % this.bitmap.width;\n      }\n\n      if (y < 0) {\n        yi = this.bitmap.height + y;\n      }\n\n      if (y >= this.bitmap.height) {\n        yi = y % this.bitmap.height;\n      }\n    }\n\n    let i = (this.bitmap.width * yi + xi) << 2;\n\n    // if out of bounds index is -1\n    if (xi < 0 || xi >= this.bitmap.width) {\n      i = -1;\n    }\n\n    if (yi < 0 || yi >= this.bitmap.height) {\n      i = -1;\n    }\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, i);\n    }\n\n    return i;\n  }\n\n  /**\n   * Returns the hex colour value of a pixel\n   * @param {number} x the x coordinate\n   * @param {number} y the y coordinate\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {number} the color of the pixel\n   */\n  getPixelColor(x, y, cb) {\n    if (typeof x !== \"number\" || typeof y !== \"number\")\n      return throwError.call(this, \"x and y must be numbers\", cb);\n\n    const idx = this.getPixelIndex(x, y);\n    const hex = this.bitmap.data.readUInt32BE(idx);\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, hex);\n    }\n\n    return hex;\n  }\n\n  getPixelColour = this.getPixelColor;\n\n  /**\n   * Returns the hex colour value of a pixel\n   * @param {number} hex color to set\n   * @param {number} x the x coordinate\n   * @param {number} y the y coordinate\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {number} the index of the pixel or -1 if not found\n   */\n  setPixelColor(hex, x, y, cb) {\n    if (\n      typeof hex !== \"number\" ||\n      typeof x !== \"number\" ||\n      typeof y !== \"number\"\n    )\n      return throwError.call(this, \"hex, x and y must be numbers\", cb);\n\n    const idx = this.getPixelIndex(x, y);\n    this.bitmap.data.writeUInt32BE(hex, idx);\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  }\n\n  setPixelColour = this.setPixelColor;\n\n  /**\n   * Determine if the image contains opaque pixels.\n   * @return {boolean} hasAlpha whether the image contains opaque pixels\n   */\n  hasAlpha() {\n    const {width, height, data} = this.bitmap;\n    const byteLen = (width * height) << 2;\n\n    for (let idx = 3; idx < byteLen; idx += 4) {\n      if (data[idx] !== 0xff) {\n        return true;\n      }\n    }\n\n    return false;\n  }\n\n  /**\n   * Iterate scan through a region of the bitmap\n   * @param {number} x the x coordinate to begin the scan at\n   * @param {number} y the y coordinate to begin the scan at\n   * @param w the width of the scan region\n   * @param h the height of the scan region\n   * @returns {IterableIterator<{x: number, y: number, idx: number, image: Jimp}>}\n   */\n  scanIterator(x, y, w, h) {\n    if (typeof x !== \"number\" || typeof y !== \"number\") {\n      return throwError.call(this, \"x and y must be numbers\");\n    }\n\n    if (typeof w !== \"number\" || typeof h !== \"number\") {\n      return throwError.call(this, \"w and h must be numbers\");\n    }\n\n    return scanIterator(this, x, y, w, h);\n  }\n}\n\nexport function addConstants(constants, jimpInstance = Jimp) {\n  Object.entries(constants).forEach(([name, value]) => {\n    jimpInstance[name] = value;\n  });\n}\n\nexport function addJimpMethods(methods, jimpInstance = Jimp) {\n  Object.entries(methods).forEach(([name, value]) => {\n    jimpInstance.prototype[name] = value;\n  });\n}\n\naddConstants(constants);\naddJimpMethods({ composite });\n\nJimp.__extraConstructors = [];\n\n/**\n * Allow client libs to add new ways to build a Jimp object.\n * @param {string} name identify the extra constructor.\n * @param {function} test a function that returns true when it accepts the arguments passed to the main constructor.\n * @param {function} run where the magic happens.\n */\nJimp.appendConstructorOption = function (name, test, run) {\n  Jimp.__extraConstructors.push({ name, test, run });\n};\n\n/**\n * Read an image from a file or a Buffer. Takes the same args as the constructor\n * @returns {Promise} a promise\n */\nJimp.read = function (...args) {\n  return new Promise((resolve, reject) => {\n    // eslint-disable-next-line no-new\n    new Jimp(...args, (err, image) => {\n      if (err) reject(err);\n      else resolve(image);\n    });\n  });\n};\n\nJimp.create = Jimp.read;\n\n/**\n * A static helper method that converts RGBA values to a single integer value\n * @param {number} r the red value (0-255)\n * @param {number} g the green value (0-255)\n * @param {number} b the blue value (0-255)\n * @param {number} a the alpha value (0-255)\n * @param {function(Error, Jimp)} cb (optional) A callback for when complete\n * @returns {number} an single integer colour value\n */\nJimp.rgbaToInt = function (r, g, b, a, cb) {\n  if (\n    typeof r !== \"number\" ||\n    typeof g !== \"number\" ||\n    typeof b !== \"number\" ||\n    typeof a !== \"number\"\n  ) {\n    return throwError.call(this, \"r, g, b and a must be numbers\", cb);\n  }\n\n  if (r < 0 || r > 255) {\n    return throwError.call(this, \"r must be between 0 and 255\", cb);\n  }\n\n  if (g < 0 || g > 255) {\n    throwError.call(this, \"g must be between 0 and 255\", cb);\n  }\n\n  if (b < 0 || b > 255) {\n    return throwError.call(this, \"b must be between 0 and 255\", cb);\n  }\n\n  if (a < 0 || a > 255) {\n    return throwError.call(this, \"a must be between 0 and 255\", cb);\n  }\n\n  let i = (r & 0xff);\n  i <<= 8;\n  i |= (g & 0xff)\n  i <<= 8;\n  i |= (b & 0xff)\n  i <<= 8;\n  i |= (a & 0xff);\n\n  // Ensure sign is correct\n  i >>>= 0;\n\n  if (isNodePattern(cb)) {\n    cb.call(this, null, i);\n  }\n\n  return i;\n};\n\n/**\n * A static helper method that converts RGBA values to a single integer value\n * @param {number} i a single integer value representing an RGBA colour (e.g. 0xFF0000FF for red)\n * @param {function(Error, Jimp)} cb (optional) A callback for when complete\n * @returns {object} an object with the properties r, g, b and a representing RGBA values\n */\nJimp.intToRGBA = function (i, cb) {\n  if (typeof i !== \"number\") {\n    return throwError.call(this, \"i must be a number\", cb);\n  }\n\n  const rgba = {};\n\n  rgba.r = Math.floor(i / Math.pow(256, 3));\n  rgba.g = Math.floor((i - rgba.r * Math.pow(256, 3)) / Math.pow(256, 2));\n  rgba.b = Math.floor(\n    (i - rgba.r * Math.pow(256, 3) - rgba.g * Math.pow(256, 2)) /\n      Math.pow(256, 1)\n  );\n  rgba.a = Math.floor(\n    (i -\n      rgba.r * Math.pow(256, 3) -\n      rgba.g * Math.pow(256, 2) -\n      rgba.b * Math.pow(256, 1)) /\n      Math.pow(256, 0)\n  );\n\n  if (isNodePattern(cb)) {\n    cb.call(this, null, rgba);\n  }\n\n  return rgba;\n};\n\n/**\n * Converts a css color (Hex, 8-digit (RGBA) Hex, RGB, RGBA, HSL, HSLA, HSV, HSVA, Named) to a hex number\n * @param {string} cssColor a number\n * @returns {number} a hex number representing a color\n */\nJimp.cssColorToHex = function (cssColor) {\n  cssColor = cssColor || 0; // 0, null, undefined, NaN\n\n  if (typeof cssColor === \"number\") return Number(cssColor);\n\n  return parseInt(tinyColor(cssColor).toHex8(), 16);\n};\n\n/**\n * Limits a number to between 0 or 255\n * @param {number} n a number\n * @returns {number} the number limited to between 0 or 255\n */\nJimp.limit255 = function (n) {\n  n = Math.max(n, 0);\n  n = Math.min(n, 255);\n\n  return n;\n};\n\n/**\n * Diffs two images and returns\n * @param {Jimp} img1 a Jimp image to compare\n * @param {Jimp} img2 a Jimp image to compare\n * @param {number} threshold (optional) a number, 0 to 1, the smaller the value the more sensitive the comparison (default: 0.1)\n * @returns {object} an object { percent: percent similar, diff: a Jimp image highlighting differences }\n */\nJimp.diff = function (img1, img2, threshold = 0.1) {\n  if (!(img1 instanceof Jimp) || !(img2 instanceof Jimp))\n    return throwError.call(this, \"img1 and img2 must be an Jimp images\");\n\n  const bmp1 = img1.bitmap;\n  const bmp2 = img2.bitmap;\n\n  if (bmp1.width !== bmp2.width || bmp1.height !== bmp2.height) {\n    if (bmp1.width * bmp1.height > bmp2.width * bmp2.height) {\n      // img1 is bigger\n      img1 = img1.cloneQuiet().resize(bmp2.width, bmp2.height);\n    } else {\n      // img2 is bigger (or they are the same in area)\n      img2 = img2.cloneQuiet().resize(bmp1.width, bmp1.height);\n    }\n  }\n\n  if (typeof threshold !== \"number\" || threshold < 0 || threshold > 1) {\n    return throwError.call(this, \"threshold must be a number between 0 and 1\");\n  }\n\n  const diff = new Jimp(bmp1.width, bmp1.height, 0xffffffff);\n\n  const numDiffPixels = pixelMatch(\n    bmp1.data,\n    bmp2.data,\n    diff.bitmap.data,\n    diff.bitmap.width,\n    diff.bitmap.height,\n    { threshold }\n  );\n\n  return {\n    percent: numDiffPixels / (diff.bitmap.width * diff.bitmap.height),\n    image: diff,\n  };\n};\n\n/**\n * Calculates the hamming distance of two images based on their perceptual hash\n * @param {Jimp} img1 a Jimp image to compare\n * @param {Jimp} img2 a Jimp image to compare\n * @returns {number} a number ranging from 0 to 1, 0 means they are believed to be identical\n */\nJimp.distance = function (img1, img2) {\n  const phash = new ImagePHash();\n  const hash1 = phash.getHash(img1);\n  const hash2 = phash.getHash(img2);\n\n  return phash.distance(hash1, hash2);\n};\n\n/**\n * Calculates the hamming distance of two images based on their perceptual hash\n * @param {hash} hash1 a pHash\n * @param {hash} hash2 a pHash\n * @returns {number} a number ranging from 0 to 1, 0 means they are believed to be identical\n */\nJimp.compareHashes = function (hash1, hash2) {\n  const phash = new ImagePHash();\n\n  return phash.distance(hash1, hash2);\n};\n\n/**\n * Compute color difference\n * 0 means no difference, 1 means maximum difference.\n * @param {number} rgba1:    first color to compare.\n * @param {number} rgba2:    second color to compare.\n * Both parameters must be an color object {r:val, g:val, b:val, a:val}\n * Where `a` is optional and `val` is an integer between 0 and 255.\n * @returns {number} float between 0 and 1.\n */\nJimp.colorDiff = function (rgba1, rgba2) {\n  const pow = (n) => Math.pow(n, 2);\n  const { max } = Math;\n  const maxVal = 255 * 255 * 3;\n\n  if (rgba1.a !== 0 && !rgba1.a) {\n    rgba1.a = 255;\n  }\n\n  if (rgba2.a !== 0 && !rgba2.a) {\n    rgba2.a = 255;\n  }\n\n  return (\n    (max(pow(rgba1.r - rgba2.r), pow(rgba1.r - rgba2.r - rgba1.a + rgba2.a)) +\n      max(pow(rgba1.g - rgba2.g), pow(rgba1.g - rgba2.g - rgba1.a + rgba2.a)) +\n      max(pow(rgba1.b - rgba2.b), pow(rgba1.b - rgba2.b - rgba1.a + rgba2.a))) /\n    maxVal\n  );\n};\n\n/**\n * Helper to create Jimp methods that emit events before and after its execution.\n * @param {string} methodName   The name to be appended to Jimp prototype.\n * @param {string} evName       The event name to be called.\n *                     It will be prefixed by `before-` and emitted when on method call.\n *                     It will be appended by `ed` and emitted after the method run.\n * @param {function} method       A function implementing the method itself.\n * It will also create a quiet version that will not emit events, to not\n * mess the user code with many `changed` event calls. You can call with\n * `methodName + \"Quiet\"`.\n *\n * The emitted event comes with a object parameter to the listener with the\n * `methodName` as one attribute.\n */\nexport function jimpEvMethod(methodName, evName, method) {\n  const evNameBefore = \"before-\" + evName;\n  const evNameAfter = evName.replace(/e$/, \"\") + \"ed\";\n\n  Jimp.prototype[methodName] = function (...args) {\n    let wrappedCb;\n    const cb = args[method.length - 1];\n    const jimpInstance = this;\n\n    if (typeof cb === \"function\") {\n      wrappedCb = function (...args) {\n        const [err, data] = args;\n\n        if (err) {\n          jimpInstance.emitError(methodName, err);\n        } else {\n          jimpInstance.emitMulti(methodName, evNameAfter, {\n            [methodName]: data,\n          });\n        }\n\n        cb.apply(this, args);\n      };\n\n      args[args.length - 1] = wrappedCb;\n    } else {\n      wrappedCb = false;\n    }\n\n    this.emitMulti(methodName, evNameBefore);\n\n    let result;\n\n    try {\n      result = method.apply(this, args);\n\n      if (!wrappedCb) {\n        this.emitMulti(methodName, evNameAfter, {\n          [methodName]: result,\n        });\n      }\n    } catch (error) {\n      error.methodName = methodName;\n      this.emitError(methodName, error);\n    }\n\n    return result;\n  };\n\n  Jimp.prototype[methodName + \"Quiet\"] = method;\n}\n\n/**\n * Creates a new image that is a clone of this one.\n * @param {function(Error, Jimp)} cb (optional) A callback for when complete\n * @returns the new image\n */\njimpEvMethod(\"clone\", \"clone\", function (cb) {\n  const clone = new Jimp(this);\n\n  if (isNodePattern(cb)) {\n    cb.call(clone, null, clone);\n  }\n\n  return clone;\n});\n\n/**\n * Simplify jimpEvMethod call for the common `change` evName.\n * @param {string} methodName name of the method\n * @param {function} method to watch changes for\n */\nexport function jimpEvChange(methodName, method) {\n  jimpEvMethod(methodName, \"change\", method);\n}\n\n/**\n * Sets the type of the image (RGB or RGBA) when saving as PNG format (default is RGBA)\n * @param b A Boolean, true to use RGBA or false to use RGB\n * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\njimpEvChange(\"background\", function (hex, cb) {\n  if (typeof hex !== \"number\") {\n    return throwError.call(this, \"hex must be a hexadecimal rgba value\", cb);\n  }\n\n  this._background = hex;\n\n  if (isNodePattern(cb)) {\n    cb.call(this, null, this);\n  }\n\n  return this;\n});\n\n/**\n * Scans through a region of the bitmap, calling a function for each pixel.\n * @param {number} x the x coordinate to begin the scan at\n * @param {number} y the y coordinate to begin the scan at\n * @param w the width of the scan region\n * @param h the height of the scan region\n * @param f a function to call on even pixel; the (x, y) position of the pixel\n * and the index of the pixel in the bitmap buffer are passed to the function\n * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\njimpEvChange(\"scan\", function (x, y, w, h, f, cb) {\n  if (typeof x !== \"number\" || typeof y !== \"number\") {\n    return throwError.call(this, \"x and y must be numbers\", cb);\n  }\n\n  if (typeof w !== \"number\" || typeof h !== \"number\") {\n    return throwError.call(this, \"w and h must be numbers\", cb);\n  }\n\n  if (typeof f !== \"function\") {\n    return throwError.call(this, \"f must be a function\", cb);\n  }\n\n  const result = scan(this, x, y, w, h, f);\n\n  if (isNodePattern(cb)) {\n    cb.call(this, null, result);\n  }\n\n  return result;\n});\n\nif (process.env.ENVIRONMENT === \"BROWSER\") {\n  // For use in a web browser or web worker\n  /* global self */\n  let gl;\n\n  if (typeof window !== \"undefined\" && typeof window === \"object\") {\n    gl = window;\n  }\n\n  if (typeof self !== \"undefined\" && typeof self === \"object\") {\n    gl = self;\n  }\n\n  gl.Jimp = Jimp;\n  gl.Buffer = Buffer;\n}\n\nexport { addType } from \"./utils/mime\";\n\nexport default Jimp;\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAyC;AAAA;AAAA;AAAA;AAAA;AAAA;AAEzC,MAAMA,QAAQ,GACZ,kEAAkE;;AAEpE;AACA;AACA,MAAMC,aAAa,GAAG,CAACC,GAAG,EAAEA,GAAG,CAAC;AAEhC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;EAC3B,MAAMC,OAAO,GAAG,IAAAC,gBAAO,EACrBA,gBAAO,CAACC,GAAG,EACXN,QAAQ,CAACO,KAAK,CAAC,CAAC,EAAEJ,CAAC,CAAC,CACrB,CAAC,IAAIK,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC;EAC9BR,aAAa,CAACS,IAAI,CAACN,OAAO,CAACO,MAAM,CAAC;AACpC;;AAEA;AACA,SAASC,IAAI,GAAG,CAAC;;AAEjB;;AAEA,SAASC,aAAa,CAACC,IAAI,EAAE;EAC3B,OACEC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,IAAI,CAAC,CAACK,WAAW,EAAE,CAACC,OAAO,CAAC,aAAa,CAAC,GACzE,CAAC,CAAC;AAEN;;AAEA;AACA;AACA,SAASC,qBAAqB,CAACC,WAAW,EAAE;EAC1C,MAAMC,MAAM,GAAGC,MAAM,CAACC,KAAK,CAACH,WAAW,CAACI,UAAU,CAAC;EACnD,MAAMC,IAAI,GAAG,IAAIC,UAAU,CAACN,WAAW,CAAC;EAExC,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoB,MAAM,CAACZ,MAAM,EAAE,EAAER,CAAC,EAAE;IACtCoB,MAAM,CAACpB,CAAC,CAAC,GAAGwB,IAAI,CAACxB,CAAC,CAAC;EACrB;EAEA,OAAOoB,MAAM;AACf;AAEA,SAASM,WAAW,CAACC,OAAO,EAAEC,EAAE,EAAE;EAChC,IAAAC,gBAAO,EAACF,OAAO,EAAE,CAACG,GAAG,EAAEC,IAAI,KAAK;IAC9B,IAAID,GAAG,EAAE;MACP,OAAOF,EAAE,CAACE,GAAG,CAAC;IAChB;IAEA,IAAI,OAAOC,IAAI,KAAK,QAAQ,IAAIV,MAAM,CAACW,QAAQ,CAACD,IAAI,CAAC,EAAE;MACrD,OAAOH,EAAE,CAAC,IAAI,EAAEG,IAAI,CAAC;IACvB;IAEA,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIrB,aAAa,CAACqB,IAAI,CAAC,EAAE;MACnD,OAAOH,EAAE,CAAC,IAAI,EAAEV,qBAAqB,CAACa,IAAI,CAAC,CAAC;IAC9C;IAEA,OAAO,IAAIE,KAAK,CAAE,+BAA8BN,OAAO,CAACO,GAAI,GAAE,CAAC;EACjE,CAAC,CAAC;AACJ;AAEA,SAASC,kBAAkB,CAACC,GAAG,EAAER,EAAE,EAAE;EACnC,IACES,WAAE,IACF,OAAOA,WAAE,CAACC,QAAQ,KAAK,UAAU,IACjC,CAACF,GAAG,CAACG,KAAK,CAAC,qBAAqB,CAAC,EACjC;IACAF,WAAE,CAACC,QAAQ,CAACF,GAAG,EAAER,EAAE,CAAC;EACtB,CAAC,MAAM;IACLF,WAAW,CAAC;MAAEQ,GAAG,EAAEE;IAAI,CAAC,EAAER,EAAE,CAAC;EAC/B;AACF;AAEA,SAASY,aAAa,CAACC,GAAG,EAAE;EAC1B,OACEA,GAAG,IACH,OAAOA,GAAG,KAAK,QAAQ,IACvB,OAAOA,GAAG,CAACC,KAAK,KAAK,QAAQ,IAC7B,OAAOD,GAAG,CAACE,MAAM,KAAK,QAAQ,KAC7BtB,MAAM,CAACW,QAAQ,CAACS,GAAG,CAACV,IAAI,CAAC,IACxBU,GAAG,CAACV,IAAI,YAAYN,UAAU,IAC7B,OAAOmB,iBAAiB,KAAK,UAAU,IACtCH,GAAG,CAACV,IAAI,YAAYa,iBAAkB,CAAC,KAC1CH,GAAG,CAACV,IAAI,CAACvB,MAAM,KAAKiC,GAAG,CAACC,KAAK,GAAGD,GAAG,CAACE,MAAM,GAAG,CAAC,IAC7CF,GAAG,CAACV,IAAI,CAACvB,MAAM,KAAKiC,GAAG,CAACC,KAAK,GAAGD,GAAG,CAACE,MAAM,GAAG,CAAC,CAAC;AAErD;AAEA,SAASE,qBAAqB,CAACzB,MAAM,EAAE;EACrC,IAAIA,MAAM,CAACZ,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;IAC3B,MAAM,IAAIyB,KAAK,CAAC,4BAA4B,CAAC;EAC/C;EAEA,MAAMa,UAAU,GAAGzB,MAAM,CAAC0B,WAAW,CAAE3B,MAAM,CAACZ,MAAM,GAAG,CAAC,GAAI,CAAC,CAAC;EAC9D,IAAIwC,CAAC,GAAG,CAAC;EAET,KAAK,IAAIhD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoB,MAAM,CAACZ,MAAM,EAAER,CAAC,EAAE,EAAE;IACtC8C,UAAU,CAACE,CAAC,CAAC,GAAG5B,MAAM,CAACpB,CAAC,CAAC;IAEzB,IAAI,CAACA,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;MACrB8C,UAAU,CAAC,EAAEE,CAAC,CAAC,GAAG,GAAG;IACvB;IAEAA,CAAC,EAAE;EACL;EAEA,OAAOF,UAAU;AACnB;AAEA,MAAMG,WAAW,GAAG;EAClBlB,IAAI,EAAE,IAAI;EACVW,KAAK,EAAE,IAAI;EACXC,MAAM,EAAE;AACV,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMO,IAAI,SAASC,eAAY,CAAC;EAC9B;EACA;EACA;EACA;;EAGA;;EAGA;;EAGA;;EAGA;;EAGAC,WAAW,GAAU;IAAA,kCAANC,IAAI;MAAJA,IAAI;IAAA;IACjB,KAAK,EAAE;IAAC,gCAfDJ,WAAW;IAAA,qCAGN,UAAU;IAAA,uCAGRC,IAAI,CAACI,QAAQ;IAAA,+BAGrB,IAAI;IAAA,+BAGJ,IAAI;IAAA,oCA2WEC,IAAI,IAAK,IAAAC,kBAAS,EAAC,IAAI,CAACC,KAAK,EAAE,IAAI,EAAEF,IAAI,CAAC;IAAA,wCAkCtCG,IAAI,IAAK,IAAAF,kBAAS,EAAC,IAAI,CAACG,SAAS,EAAE,IAAI,EAAED,IAAI,CAAC;IAAA,mCAqEpDE,sBAAS;IAAA,wCAEJC,2BAAc;IAAA,wCAiGd,IAAI,CAACC,aAAa;IAAA,wCA4BlB,IAAI,CAACC,aAAa;IA5kBjC,MAAMC,YAAY,GAAG,IAAI;IACzB,IAAIpC,EAAE,GAAGnB,IAAI;IAEb,IAAIC,aAAa,CAAC2C,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1BA,IAAI,CAAC,CAAC,CAAC,GAAGnC,qBAAqB,CAACmC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC1C;IAEA,SAASY,MAAM,GAAU;MAAA,mCAANZ,IAAI;QAAJA,IAAI;MAAA;MACrB,MAAM,CAACvB,GAAG,CAAC,GAAGuB,IAAI;MAClB,MAAMa,MAAM,GAAGpC,GAAG,IAAI,CAAC,CAAC;MACxBoC,MAAM,CAACC,UAAU,GAAG,aAAa;MAEjCC,UAAU,CAAC,MAAM;QACf;QACA,IAAItC,GAAG,IAAIF,EAAE,KAAKnB,IAAI,EAAE;UACtBuD,YAAY,CAACK,SAAS,CAAC,aAAa,EAAEvC,GAAG,CAAC;QAC5C,CAAC,MAAM,IAAI,CAACA,GAAG,EAAE;UACfkC,YAAY,CAACM,SAAS,CAAC,aAAa,EAAE,aAAa,CAAC;QACtD;QAEA1C,EAAE,CAACb,IAAI,CAACiD,YAAY,EAAE,GAAGX,IAAI,CAAC;MAChC,CAAC,EAAE,CAAC,CAAC;IACP;IAEA,IACG,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAC1DkB,QAAQ,CAAClB,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAIkB,QAAQ,CAAClB,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAE,EAChD;MACA;MACA,MAAMmB,CAAC,GAAGD,QAAQ,CAAClB,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC/B,MAAMoB,CAAC,GAAGF,QAAQ,CAAClB,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC/BzB,EAAE,GAAGyB,IAAI,CAAC,CAAC,CAAC;;MAEZ;MACA,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;QAC/B,IAAI,CAACqB,WAAW,GAAGrB,IAAI,CAAC,CAAC,CAAC;QAC1BzB,EAAE,GAAGyB,IAAI,CAAC,CAAC,CAAC;MACd;;MAEA;MACA,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;QAC/B,IAAI,CAACqB,WAAW,GAAGxB,IAAI,CAACyB,aAAa,CAACtB,IAAI,CAAC,CAAC,CAAC,CAAC;QAC9CzB,EAAE,GAAGyB,IAAI,CAAC,CAAC,CAAC;MACd;MAEA,IAAI,OAAOzB,EAAE,KAAK,WAAW,EAAE;QAC7BA,EAAE,GAAGnB,IAAI;MACX;MAEA,IAAI,OAAOmB,EAAE,KAAK,UAAU,EAAE;QAC5B,OAAOgD,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAE,uBAAuB,EAAEkD,MAAM,CAAC;MAC/D;MAEA,IAAI,CAACY,MAAM,GAAG;QACZ9C,IAAI,EAAEV,MAAM,CAACC,KAAK,CAACkD,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC;QAC7B/B,KAAK,EAAE8B,CAAC;QACR7B,MAAM,EAAE8B;MACV,CAAC;MAED,KAAK,IAAIzE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC6E,MAAM,CAAC9C,IAAI,CAACvB,MAAM,EAAER,CAAC,IAAI,CAAC,EAAE;QACnD,IAAI,CAAC6E,MAAM,CAAC9C,IAAI,CAAC+C,aAAa,CAAC,IAAI,CAACJ,WAAW,EAAE1E,CAAC,CAAC;MACrD;MAEAiE,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;IACpB,CAAC,MAAM,IAAI,OAAOZ,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAIA,IAAI,CAAC,CAAC,CAAC,CAACnB,GAAG,EAAE;MACrDN,EAAE,GAAGyB,IAAI,CAAC,CAAC,CAAC,IAAI5C,IAAI;MAEpB,IAAI,OAAOmB,EAAE,KAAK,UAAU,EAAE;QAC5B,OAAOgD,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAE,uBAAuB,EAAEkD,MAAM,CAAC;MAC/D;MAEAvC,WAAW,CAAC2B,IAAI,CAAC,CAAC,CAAC,EAAE,CAACvB,GAAG,EAAEC,IAAI,KAAK;QAClC,IAAID,GAAG,EAAE;UACP,OAAO8C,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAEe,GAAG,EAAEmC,MAAM,CAAC;QAC3C;QAEA,IAAI,CAACc,WAAW,CAAChD,IAAI,EAAEsB,IAAI,CAAC,CAAC,CAAC,CAACnB,GAAG,EAAE+B,MAAM,CAAC;MAC7C,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIZ,IAAI,CAAC,CAAC,CAAC,YAAYH,IAAI,EAAE;MAClC;MACA,MAAM,CAAC8B,QAAQ,CAAC,GAAG3B,IAAI;MACvBzB,EAAE,GAAGyB,IAAI,CAAC,CAAC,CAAC;MAEZ,IAAI,OAAOzB,EAAE,KAAK,WAAW,EAAE;QAC7BA,EAAE,GAAGnB,IAAI;MACX;MAEA,IAAI,OAAOmB,EAAE,KAAK,UAAU,EAAE;QAC5B,OAAOgD,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAE,uBAAuB,EAAEkD,MAAM,CAAC;MAC/D;MAEA,IAAI,CAACY,MAAM,GAAG;QACZ9C,IAAI,EAAEV,MAAM,CAAC4D,IAAI,CAACD,QAAQ,CAACH,MAAM,CAAC9C,IAAI,CAAC;QACvCW,KAAK,EAAEsC,QAAQ,CAACH,MAAM,CAACnC,KAAK;QAC5BC,MAAM,EAAEqC,QAAQ,CAACH,MAAM,CAAClC;MAC1B,CAAC;MAED,IAAI,CAACuC,QAAQ,GAAGF,QAAQ,CAACE,QAAQ;MACjC,IAAI,CAACC,aAAa,GAAGH,QAAQ,CAACG,aAAa;MAC3C,IAAI,CAACC,gBAAgB,GAAGJ,QAAQ,CAACI,gBAAgB;MACjD,IAAI,CAACC,WAAW,GAAGL,QAAQ,CAACK,WAAW;MACvC,IAAI,CAACC,KAAK,GAAGN,QAAQ,CAACM,KAAK;MAC3B,IAAI,CAACZ,WAAW,GAAGM,QAAQ,CAACN,WAAW;MACvC,IAAI,CAACa,aAAa,GAAGP,QAAQ,CAACO,aAAa;MAE3CtB,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;IACpB,CAAC,MAAM,IAAIzB,aAAa,CAACa,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;MACjC,MAAM,CAACmC,SAAS,CAAC,GAAGnC,IAAI;MACxBzB,EAAE,GAAGyB,IAAI,CAAC,CAAC,CAAC,IAAI5C,IAAI;MAEpB,MAAMgF,MAAM,GACVD,SAAS,CAAC9C,KAAK,GAAG8C,SAAS,CAAC7C,MAAM,GAAG,CAAC,KAAK6C,SAAS,CAACzD,IAAI,CAACvB,MAAM;MAClE,MAAMY,MAAM,GAAGqE,MAAM,GACjBpE,MAAM,CAAC4D,IAAI,CAACO,SAAS,CAACzD,IAAI,CAAC,GAC3Bc,qBAAqB,CAAC2C,SAAS,CAACzD,IAAI,CAAC;MAEzC,IAAI,CAAC8C,MAAM,GAAG;QACZ9C,IAAI,EAAEX,MAAM;QACZsB,KAAK,EAAE8C,SAAS,CAAC9C,KAAK;QACtBC,MAAM,EAAE6C,SAAS,CAAC7C;MACpB,CAAC;MAEDsB,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;IACpB,CAAC,MAAM,IAAI,OAAOZ,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MACtC;MACA,MAAME,IAAI,GAAGF,IAAI,CAAC,CAAC,CAAC;MACpBzB,EAAE,GAAGyB,IAAI,CAAC,CAAC,CAAC;MAEZ,IAAI,OAAOzB,EAAE,KAAK,WAAW,EAAE;QAC7BA,EAAE,GAAGnB,IAAI;MACX;MAEA,IAAI,OAAOmB,EAAE,KAAK,UAAU,EAAE;QAC5B,OAAOgD,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAE,uBAAuB,EAAEkD,MAAM,CAAC;MAC/D;MAEA9B,kBAAkB,CAACoB,IAAI,EAAE,CAACzB,GAAG,EAAEC,IAAI,KAAK;QACtC,IAAID,GAAG,EAAE;UACP,OAAO8C,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAEe,GAAG,EAAEmC,MAAM,CAAC;QAC3C;QAEA,IAAI,CAACc,WAAW,CAAChD,IAAI,EAAEwB,IAAI,EAAEU,MAAM,CAAC;MACtC,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,OAAOZ,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAIhC,MAAM,CAACW,QAAQ,CAACqB,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;MAClE;MACA,MAAMtB,IAAI,GAAGsB,IAAI,CAAC,CAAC,CAAC;MACpBzB,EAAE,GAAGyB,IAAI,CAAC,CAAC,CAAC;MAEZ,IAAI,OAAOzB,EAAE,KAAK,UAAU,EAAE;QAC5B,OAAOgD,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAE,uBAAuB,EAAEkD,MAAM,CAAC;MAC/D;MAEA,IAAI,CAACc,WAAW,CAAChD,IAAI,EAAE,IAAI,EAAEkC,MAAM,CAAC;IACtC,CAAC,MAAM;MACL;MACA;MACArC,EAAE,GAAGyB,IAAI,CAACA,IAAI,CAAC7C,MAAM,GAAG,CAAC,CAAC;MAE1B,IAAI,OAAOoB,EAAE,KAAK,UAAU,EAAE;QAC5B;QACAA,EAAE,GAAGyB,IAAI,CAACA,IAAI,CAAC7C,MAAM,GAAG,CAAC,CAAC;QAE1B,IAAI,OAAOoB,EAAE,KAAK,UAAU,EAAE;UAC5BA,EAAE,GAAGnB,IAAI;QACX;MACF;MAEA,MAAMiF,gBAAgB,GAAGxC,IAAI,CAACyC,mBAAmB,CAACC,IAAI,CAAEC,CAAC,IACvDA,CAAC,CAAClF,IAAI,CAAC,GAAG0C,IAAI,CAAC,CAChB;MAED,IAAIqC,gBAAgB,EAAE;QACpB,IAAII,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;UAC/BN,gBAAgB,CAACO,GAAG,CAAClF,IAAI,CAAC,IAAI,EAAEgF,OAAO,EAAEC,MAAM,EAAE,GAAG3C,IAAI,CAAC;QAC3D,CAAC,CAAC,CACC6C,IAAI,CAAC,MAAMjC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAC9BkC,KAAK,CAAClC,MAAM,CAAC;MAClB,CAAC,MAAM;QACL,OAAOW,iBAAU,CAAC7D,IAAI,CACpB,IAAI,EACJ,iDAAiD,GAC/C,2DAA2D,EAC7DkD,MAAM,CACP;MACH;IACF;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEc,WAAW,CAAChD,IAAI,EAAEwB,IAAI,EAAEU,MAAM,EAAE;IAC9Bc,wBAAW,CAAChE,IAAI,CAAC,IAAI,EAAEgB,IAAI,EAAE,IAAI,EAAEkC,MAAM,CAAC;EAC5C;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEmC,IAAI,CAACC,IAAI,EAAEzE,EAAE,EAAE;IACb,IAAI,OAAOyE,IAAI,KAAK,SAAS,EAAE;MAC7B,OAAOzB,iBAAU,CAAC7D,IAAI,CACpB,IAAI,EACJ,wDAAwD,EACxDa,EAAE,CACH;IACH;IAEA,IAAI,CAAC0D,KAAK,GAAGe,IAAI;IAEjB,IAAI,IAAAC,oBAAa,EAAC1E,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACb,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEuD,SAAS,CAACH,UAAU,EAAEoC,SAAS,EAAa;IAAA,IAAXxE,IAAI,uEAAG,CAAC,CAAC;IACxCA,IAAI,GAAGnB,MAAM,CAAC4F,MAAM,CAACzE,IAAI,EAAE;MAAEoC,UAAU;MAAEoC;IAAU,CAAC,CAAC;IACrD,IAAI,CAACE,IAAI,CAAC,KAAK,EAAE1E,IAAI,CAAC;IAEtB,IAAIoC,UAAU,EAAE;MACd,IAAI,CAACsC,IAAI,CAACtC,UAAU,EAAEpC,IAAI,CAAC;IAC7B;IAEA,IAAI,CAAC0E,IAAI,CAACF,SAAS,EAAExE,IAAI,CAAC;EAC5B;EAEAsC,SAAS,CAACF,UAAU,EAAErC,GAAG,EAAE;IACzB,IAAI,CAACwC,SAAS,CAACH,UAAU,EAAE,OAAO,EAAErC,GAAG,CAAC;EAC1C;;EAEA;AACF;AACA;AACA;EACE4E,SAAS,GAAG;IACV,OAAO,IAAI,CAAC7B,MAAM,CAAClC,MAAM;EAC3B;;EAEA;AACF;AACA;AACA;EACEgE,QAAQ,GAAG;IACT,OAAO,IAAI,CAAC9B,MAAM,CAACnC,KAAK;EAC1B;;EAEA;AACF;AACA;AACA;EACEkE,OAAO,GAAG;IACR,OACE,QAAQ,IACP,IAAI,CAAC/B,MAAM,KAAK5B,WAAW,GACxB,YAAY,GACZ,IAAI,CAAC4B,MAAM,CAACnC,KAAK,GAAG,GAAG,GAAG,IAAI,CAACmC,MAAM,CAAClC,MAAM,CAAC,GACjD,GAAG;EAEP;;EAEA;AACF;AACA;AACA;EACE7B,QAAQ,GAAG;IACT,OAAO,eAAe;EACxB;;EAEA;AACF;AACA;AACA;EACE+F,OAAO,GAAG;IACR,MAAMnD,IAAI,GAAG,IAAI,CAAC6B,aAAa,IAAIrC,IAAI,CAACI,QAAQ;IAEhD,OAAOI,IAAI;EACb;;EAEA;AACF;AACA;AACA;EACEoD,YAAY,GAAG;IACb,MAAMpD,IAAI,GAAG,IAAI,CAACmD,OAAO,EAAE;IAE3B,OAAOE,IAAI,CAACD,YAAY,CAACpD,IAAI,CAAC;EAChC;;EAEA;AACF;AACA;AACA;AACA;AACA;EACED,KAAK,CAACF,IAAI,EAAE3B,EAAE,EAAE;IACd,IAAI,CAACS,WAAE,IAAI,CAACA,WAAE,CAAC2E,iBAAiB,EAAE;MAChC,MAAM,IAAI/E,KAAK,CACb,+DAA+D,CAChE;IACH;IAEA,IAAI,OAAOsB,IAAI,KAAK,QAAQ,EAAE;MAC5B,OAAOqB,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAE,uBAAuB,EAAEa,EAAE,CAAC;IAC3D;IAEA,IAAI,OAAOA,EAAE,KAAK,WAAW,EAAE;MAC7BA,EAAE,GAAGnB,IAAI;IACX;IAEA,IAAI,OAAOmB,EAAE,KAAK,UAAU,EAAE;MAC5B,OAAOgD,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAE,uBAAuB,EAAEa,EAAE,CAAC;IAC3D;IAEA,MAAM8B,IAAI,GAAGqD,IAAI,CAACE,OAAO,CAAC1D,IAAI,CAAC,IAAI,IAAI,CAACsD,OAAO,EAAE;IACjD,MAAMK,OAAO,GAAGC,aAAI,CAACC,KAAK,CAAC7D,IAAI,CAAC;IAEhC,IAAI2D,OAAO,CAACG,GAAG,EAAE;MACfhF,WAAE,CAACiF,SAAS,CAACJ,OAAO,CAACG,GAAG,EAAE;QAAEE,SAAS,EAAE;MAAK,CAAC,CAAC;IAChD;IAEA,IAAI,CAAC3D,SAAS,CAACF,IAAI,EAAE,CAAC5B,GAAG,EAAEV,MAAM,KAAK;MACpC,IAAIU,GAAG,EAAE;QACP,OAAO8C,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAEe,GAAG,EAAEF,EAAE,CAAC;MACvC;MAEA,MAAM4F,MAAM,GAAGnF,WAAE,CAAC2E,iBAAiB,CAACzD,IAAI,CAAC;MAEzCiE,MAAM,CACHC,EAAE,CAAC,MAAM,EAAE,MAAM;QAChBD,MAAM,CAAC/D,KAAK,CAACrC,MAAM,CAAC;QACpBoG,MAAM,CAACE,GAAG,EAAE;MACd,CAAC,CAAC,CACDD,EAAE,CAAC,OAAO,EAAG3F,GAAG,IAAK;QACpB,OAAO8C,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAEe,GAAG,EAAEF,EAAE,CAAC;MACvC,CAAC,CAAC;MACJ4F,MAAM,CAACC,EAAE,CAAC,QAAQ,EAAE,MAAM;QACxB7F,EAAE,CAACb,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAC3B,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO,IAAI;EACb;EAIA;AACF;AACA;AACA;AACA;AACA;EACE4C,SAAS,CAACD,IAAI,EAAE9B,EAAE,EAAE;IAClB,IAAI8B,IAAI,KAAKR,IAAI,CAACyE,IAAI,EAAE;MACtB;MACAjE,IAAI,GAAG,IAAI,CAACmD,OAAO,EAAE;IACvB;IAEA,IAAI,OAAOnD,IAAI,KAAK,QAAQ,EAAE;MAC5B,OAAOkB,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAE,uBAAuB,EAAEa,EAAE,CAAC;IAC3D;IAEA,IAAI,OAAOA,EAAE,KAAK,UAAU,EAAE;MAC5B,OAAOgD,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAE,uBAAuB,EAAEa,EAAE,CAAC;IAC3D;IAEA,IAAI,CAACgC,SAAS,CAACF,IAAI,EAAE,UAAU5B,GAAG,EAAEC,IAAI,EAAE;MACxC,IAAID,GAAG,EAAE;QACP,OAAO8C,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAEe,GAAG,EAAEF,EAAE,CAAC;MACvC;MAEA,MAAMQ,GAAG,GAAG,OAAO,GAAGsB,IAAI,GAAG,UAAU,GAAG3B,IAAI,CAACjB,QAAQ,CAAC,QAAQ,CAAC;MACjEc,EAAE,CAACb,IAAI,CAAC,IAAI,EAAE,IAAI,EAAEqB,GAAG,CAAC;IAC1B,CAAC,CAAC;IAEF,OAAO,IAAI;EACb;EAIA;AACF;AACA;AACA;AACA;AACA;EACEwF,IAAI,CAACC,IAAI,EAAEjG,EAAE,EAAE;IACbiG,IAAI,GAAGA,IAAI,IAAI,EAAE;IAEjB,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;MAC9BjG,EAAE,GAAGiG,IAAI;MACTA,IAAI,GAAG,EAAE;IACX;IAEA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC5B,OAAOjD,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAE,uBAAuB,EAAEa,EAAE,CAAC;IAC3D;IAEA,IAAIiG,IAAI,GAAG,CAAC,IAAIA,IAAI,GAAG,EAAE,EAAE;MACzB,OAAOjD,iBAAU,CAAC7D,IAAI,CACpB,IAAI,EACJ,wCAAwC,EACxCa,EAAE,CACH;IACH;IAEA,IAAIgG,IAAI,GAAG,IAAI,CAACE,KAAK,EAAE;IACvBF,IAAI,GAAG,IAAA1H,gBAAO,EAACA,gBAAO,CAACC,GAAG,EAAEN,QAAQ,CAACO,KAAK,CAAC,CAAC,EAAEyH,IAAI,CAAC,CAAC,CAACD,IAAI,CAAC;IAE1D,OAAOA,IAAI,CAACpH,MAAM,GAAGV,aAAa,CAAC+H,IAAI,CAAC,EAAE;MACxCD,IAAI,GAAG,GAAG,GAAGA,IAAI,CAAC,CAAC;IACrB;;IAEA,IAAI,IAAAtB,oBAAa,EAAC1E,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACb,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE6G,IAAI,CAAC;IAC3B;IAEA,OAAOA,IAAI;EACb;;EAEA;AACF;AACA;AACA;EACEE,KAAK,GAAG;IACN,MAAMA,KAAK,GAAG,IAAIC,cAAU,EAAE;IAC9B,OAAOD,KAAK,CAACE,OAAO,CAAC,IAAI,CAAC;EAC5B;;EAEA;AACF;AACA;AACA;AACA;EACEC,gBAAgB,CAACC,WAAW,EAAE;IAC5B,MAAMJ,KAAK,GAAG,IAAIC,cAAU,EAAE;IAC9B,MAAMI,WAAW,GAAGL,KAAK,CAACE,OAAO,CAAC,IAAI,CAAC;IAEvC,OAAOF,KAAK,CAACM,QAAQ,CAACD,WAAW,EAAED,WAAW,CAAC;EACjD;;EAEA;AACF;AACA;AACA;AACA;AACA;;EAKE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEG,aAAa,CAACC,CAAC,EAAEC,CAAC,EAAEC,YAAY,EAAE5G,EAAE,EAAE;IACpC,IAAI6G,EAAE;IACN,IAAIC,EAAE;IAEN,IAAI,OAAOF,YAAY,KAAK,UAAU,IAAI,OAAO5G,EAAE,KAAK,WAAW,EAAE;MACnEA,EAAE,GAAG4G,YAAY;MACjBA,YAAY,GAAG,IAAI;IACrB;IAEA,IAAI,CAACA,YAAY,EAAE;MACjBA,YAAY,GAAGtF,IAAI,CAACyF,WAAW;IACjC;IAEA,IAAI,OAAOL,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE;MAClD,OAAO3D,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAE,yBAAyB,EAAEa,EAAE,CAAC;IAC7D;;IAEA;IACA0G,CAAC,GAAGM,IAAI,CAACC,KAAK,CAACP,CAAC,CAAC;IACjBC,CAAC,GAAGK,IAAI,CAACC,KAAK,CAACN,CAAC,CAAC;IACjBE,EAAE,GAAGH,CAAC;IACNI,EAAE,GAAGH,CAAC;IAEN,IAAIC,YAAY,KAAKtF,IAAI,CAACyF,WAAW,EAAE;MACrC,IAAIL,CAAC,GAAG,CAAC,EAAEG,EAAE,GAAG,CAAC;MACjB,IAAIH,CAAC,IAAI,IAAI,CAACzD,MAAM,CAACnC,KAAK,EAAE+F,EAAE,GAAG,IAAI,CAAC5D,MAAM,CAACnC,KAAK,GAAG,CAAC;MACtD,IAAI6F,CAAC,GAAG,CAAC,EAAEG,EAAE,GAAG,CAAC;MACjB,IAAIH,CAAC,IAAI,IAAI,CAAC1D,MAAM,CAAClC,MAAM,EAAE+F,EAAE,GAAG,IAAI,CAAC7D,MAAM,CAAClC,MAAM,GAAG,CAAC;IAC1D;IAEA,IAAI6F,YAAY,KAAKtF,IAAI,CAAC4F,SAAS,EAAE;MACnC,IAAIR,CAAC,GAAG,CAAC,EAAE;QACTG,EAAE,GAAG,IAAI,CAAC5D,MAAM,CAACnC,KAAK,GAAG4F,CAAC;MAC5B;MAEA,IAAIA,CAAC,IAAI,IAAI,CAACzD,MAAM,CAACnC,KAAK,EAAE;QAC1B+F,EAAE,GAAGH,CAAC,GAAG,IAAI,CAACzD,MAAM,CAACnC,KAAK;MAC5B;MAEA,IAAI6F,CAAC,GAAG,CAAC,EAAE;QACTG,EAAE,GAAG,IAAI,CAAC7D,MAAM,CAAClC,MAAM,GAAG4F,CAAC;MAC7B;MAEA,IAAIA,CAAC,IAAI,IAAI,CAAC1D,MAAM,CAAClC,MAAM,EAAE;QAC3B+F,EAAE,GAAGH,CAAC,GAAG,IAAI,CAAC1D,MAAM,CAAClC,MAAM;MAC7B;IACF;IAEA,IAAI3C,CAAC,GAAI,IAAI,CAAC6E,MAAM,CAACnC,KAAK,GAAGgG,EAAE,GAAGD,EAAE,IAAK,CAAC;;IAE1C;IACA,IAAIA,EAAE,GAAG,CAAC,IAAIA,EAAE,IAAI,IAAI,CAAC5D,MAAM,CAACnC,KAAK,EAAE;MACrC1C,CAAC,GAAG,CAAC,CAAC;IACR;IAEA,IAAI0I,EAAE,GAAG,CAAC,IAAIA,EAAE,IAAI,IAAI,CAAC7D,MAAM,CAAClC,MAAM,EAAE;MACtC3C,CAAC,GAAG,CAAC,CAAC;IACR;IAEA,IAAI,IAAAsG,oBAAa,EAAC1E,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACb,IAAI,CAAC,IAAI,EAAE,IAAI,EAAEf,CAAC,CAAC;IACxB;IAEA,OAAOA,CAAC;EACV;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE8D,aAAa,CAACwE,CAAC,EAAEC,CAAC,EAAE3G,EAAE,EAAE;IACtB,IAAI,OAAO0G,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAChD,OAAO3D,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAE,yBAAyB,EAAEa,EAAE,CAAC;IAE7D,MAAMmH,GAAG,GAAG,IAAI,CAACV,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACpC,MAAMS,GAAG,GAAG,IAAI,CAACnE,MAAM,CAAC9C,IAAI,CAACkH,YAAY,CAACF,GAAG,CAAC;IAE9C,IAAI,IAAAzC,oBAAa,EAAC1E,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACb,IAAI,CAAC,IAAI,EAAE,IAAI,EAAEiI,GAAG,CAAC;IAC1B;IAEA,OAAOA,GAAG;EACZ;EAIA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEjF,aAAa,CAACiF,GAAG,EAAEV,CAAC,EAAEC,CAAC,EAAE3G,EAAE,EAAE;IAC3B,IACE,OAAOoH,GAAG,KAAK,QAAQ,IACvB,OAAOV,CAAC,KAAK,QAAQ,IACrB,OAAOC,CAAC,KAAK,QAAQ,EAErB,OAAO3D,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAE,8BAA8B,EAAEa,EAAE,CAAC;IAElE,MAAMmH,GAAG,GAAG,IAAI,CAACV,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACpC,IAAI,CAAC1D,MAAM,CAAC9C,IAAI,CAAC+C,aAAa,CAACkE,GAAG,EAAED,GAAG,CAAC;IAExC,IAAI,IAAAzC,oBAAa,EAAC1E,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACb,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb;EAIA;AACF;AACA;AACA;EACEmI,QAAQ,GAAG;IACT,MAAM;MAACxG,KAAK;MAAEC,MAAM;MAAEZ;IAAI,CAAC,GAAG,IAAI,CAAC8C,MAAM;IACzC,MAAMsE,OAAO,GAAIzG,KAAK,GAAGC,MAAM,IAAK,CAAC;IAErC,KAAK,IAAIoG,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGI,OAAO,EAAEJ,GAAG,IAAI,CAAC,EAAE;MACzC,IAAIhH,IAAI,CAACgH,GAAG,CAAC,KAAK,IAAI,EAAE;QACtB,OAAO,IAAI;MACb;IACF;IAEA,OAAO,KAAK;EACd;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEK,YAAY,CAACd,CAAC,EAAEC,CAAC,EAAE/D,CAAC,EAAEC,CAAC,EAAE;IACvB,IAAI,OAAO6D,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE;MAClD,OAAO3D,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAE,yBAAyB,CAAC;IACzD;IAEA,IAAI,OAAOyD,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE;MAClD,OAAOG,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAE,yBAAyB,CAAC;IACzD;IAEA,OAAO,IAAAqI,mBAAY,EAAC,IAAI,EAAEd,CAAC,EAAEC,CAAC,EAAE/D,CAAC,EAAEC,CAAC,CAAC;EACvC;AACF;AAEO,SAAS4E,YAAY,CAACC,SAAS,EAAuB;EAAA,IAArBtF,YAAY,uEAAGd,IAAI;EACzDtC,MAAM,CAAC2I,OAAO,CAACD,SAAS,CAAC,CAACE,OAAO,CAAC,QAAmB;IAAA,IAAlB,CAACC,IAAI,EAAEC,KAAK,CAAC;IAC9C1F,YAAY,CAACyF,IAAI,CAAC,GAAGC,KAAK;EAC5B,CAAC,CAAC;AACJ;AAEO,SAASC,cAAc,CAACC,OAAO,EAAuB;EAAA,IAArB5F,YAAY,uEAAGd,IAAI;EACzDtC,MAAM,CAAC2I,OAAO,CAACK,OAAO,CAAC,CAACJ,OAAO,CAAC,SAAmB;IAAA,IAAlB,CAACC,IAAI,EAAEC,KAAK,CAAC;IAC5C1F,YAAY,CAACnD,SAAS,CAAC4I,IAAI,CAAC,GAAGC,KAAK;EACtC,CAAC,CAAC;AACJ;AAEAL,YAAY,CAACC,SAAS,CAAC;AACvBK,cAAc,CAAC;EAAEE,SAAS,EAATA;AAAU,CAAC,CAAC;AAE7B3G,IAAI,CAACyC,mBAAmB,GAAG,EAAE;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACAzC,IAAI,CAAC4G,uBAAuB,GAAG,UAAUL,IAAI,EAAE9I,IAAI,EAAEsF,GAAG,EAAE;EACxD/C,IAAI,CAACyC,mBAAmB,CAACpF,IAAI,CAAC;IAAEkJ,IAAI;IAAE9I,IAAI;IAAEsF;EAAI,CAAC,CAAC;AACpD,CAAC;;AAED;AACA;AACA;AACA;AACA/C,IAAI,CAAC6G,IAAI,GAAG,YAAmB;EAAA,mCAAN1G,IAAI;IAAJA,IAAI;EAAA;EAC3B,OAAO,IAAIyC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC;IACA,IAAI9C,IAAI,CAAC,GAAGG,IAAI,EAAE,CAACvB,GAAG,EAAEkI,KAAK,KAAK;MAChC,IAAIlI,GAAG,EAAEkE,MAAM,CAAClE,GAAG,CAAC,CAAC,KAChBiE,OAAO,CAACiE,KAAK,CAAC;IACrB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AAED9G,IAAI,CAAC+G,MAAM,GAAG/G,IAAI,CAAC6G,IAAI;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA7G,IAAI,CAACgH,SAAS,GAAG,UAAUC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE1I,EAAE,EAAE;EACzC,IACE,OAAOuI,CAAC,KAAK,QAAQ,IACrB,OAAOC,CAAC,KAAK,QAAQ,IACrB,OAAOC,CAAC,KAAK,QAAQ,IACrB,OAAOC,CAAC,KAAK,QAAQ,EACrB;IACA,OAAO1F,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAE,+BAA+B,EAAEa,EAAE,CAAC;EACnE;EAEA,IAAIuI,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,GAAG,EAAE;IACpB,OAAOvF,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAE,6BAA6B,EAAEa,EAAE,CAAC;EACjE;EAEA,IAAIwI,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,GAAG,EAAE;IACpBxF,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAE,6BAA6B,EAAEa,EAAE,CAAC;EAC1D;EAEA,IAAIyI,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,GAAG,EAAE;IACpB,OAAOzF,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAE,6BAA6B,EAAEa,EAAE,CAAC;EACjE;EAEA,IAAI0I,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,GAAG,EAAE;IACpB,OAAO1F,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAE,6BAA6B,EAAEa,EAAE,CAAC;EACjE;EAEA,IAAI5B,CAAC,GAAImK,CAAC,GAAG,IAAK;EAClBnK,CAAC,KAAK,CAAC;EACPA,CAAC,IAAKoK,CAAC,GAAG,IAAK;EACfpK,CAAC,KAAK,CAAC;EACPA,CAAC,IAAKqK,CAAC,GAAG,IAAK;EACfrK,CAAC,KAAK,CAAC;EACPA,CAAC,IAAKsK,CAAC,GAAG,IAAK;;EAEf;EACAtK,CAAC,MAAM,CAAC;EAER,IAAI,IAAAsG,oBAAa,EAAC1E,EAAE,CAAC,EAAE;IACrBA,EAAE,CAACb,IAAI,CAAC,IAAI,EAAE,IAAI,EAAEf,CAAC,CAAC;EACxB;EAEA,OAAOA,CAAC;AACV,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACAkD,IAAI,CAACqH,SAAS,GAAG,UAAUvK,CAAC,EAAE4B,EAAE,EAAE;EAChC,IAAI,OAAO5B,CAAC,KAAK,QAAQ,EAAE;IACzB,OAAO4E,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAEa,EAAE,CAAC;EACxD;EAEA,MAAMwE,IAAI,GAAG,CAAC,CAAC;EAEfA,IAAI,CAAC+D,CAAC,GAAGvB,IAAI,CAAC4B,KAAK,CAACxK,CAAC,GAAG4I,IAAI,CAAC6B,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;EACzCrE,IAAI,CAACgE,CAAC,GAAGxB,IAAI,CAAC4B,KAAK,CAAC,CAACxK,CAAC,GAAGoG,IAAI,CAAC+D,CAAC,GAAGvB,IAAI,CAAC6B,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI7B,IAAI,CAAC6B,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;EACvErE,IAAI,CAACiE,CAAC,GAAGzB,IAAI,CAAC4B,KAAK,CACjB,CAACxK,CAAC,GAAGoG,IAAI,CAAC+D,CAAC,GAAGvB,IAAI,CAAC6B,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,GAAGrE,IAAI,CAACgE,CAAC,GAAGxB,IAAI,CAAC6B,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,IACxD7B,IAAI,CAAC6B,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CACnB;EACDrE,IAAI,CAACkE,CAAC,GAAG1B,IAAI,CAAC4B,KAAK,CACjB,CAACxK,CAAC,GACAoG,IAAI,CAAC+D,CAAC,GAAGvB,IAAI,CAAC6B,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,GACzBrE,IAAI,CAACgE,CAAC,GAAGxB,IAAI,CAAC6B,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,GACzBrE,IAAI,CAACiE,CAAC,GAAGzB,IAAI,CAAC6B,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,IACzB7B,IAAI,CAAC6B,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CACnB;EAED,IAAI,IAAAnE,oBAAa,EAAC1E,EAAE,CAAC,EAAE;IACrBA,EAAE,CAACb,IAAI,CAAC,IAAI,EAAE,IAAI,EAAEqF,IAAI,CAAC;EAC3B;EAEA,OAAOA,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACAlD,IAAI,CAACyB,aAAa,GAAG,UAAU+F,QAAQ,EAAE;EACvCA,QAAQ,GAAGA,QAAQ,IAAI,CAAC,CAAC,CAAC;;EAE1B,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE,OAAOC,MAAM,CAACD,QAAQ,CAAC;EAEzD,OAAOnG,QAAQ,CAAC,IAAAqG,kBAAS,EAACF,QAAQ,CAAC,CAACG,MAAM,EAAE,EAAE,EAAE,CAAC;AACnD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA3H,IAAI,CAAC4H,QAAQ,GAAG,UAAUC,CAAC,EAAE;EAC3BA,CAAC,GAAGnC,IAAI,CAACoC,GAAG,CAACD,CAAC,EAAE,CAAC,CAAC;EAClBA,CAAC,GAAGnC,IAAI,CAACqC,GAAG,CAACF,CAAC,EAAE,GAAG,CAAC;EAEpB,OAAOA,CAAC;AACV,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA7H,IAAI,CAACgI,IAAI,GAAG,UAAUC,IAAI,EAAEC,IAAI,EAAmB;EAAA,IAAjBC,SAAS,uEAAG,GAAG;EAC/C,IAAI,EAAEF,IAAI,YAAYjI,IAAI,CAAC,IAAI,EAAEkI,IAAI,YAAYlI,IAAI,CAAC,EACpD,OAAO0B,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAE,sCAAsC,CAAC;EAEtE,MAAMuK,IAAI,GAAGH,IAAI,CAACtG,MAAM;EACxB,MAAM0G,IAAI,GAAGH,IAAI,CAACvG,MAAM;EAExB,IAAIyG,IAAI,CAAC5I,KAAK,KAAK6I,IAAI,CAAC7I,KAAK,IAAI4I,IAAI,CAAC3I,MAAM,KAAK4I,IAAI,CAAC5I,MAAM,EAAE;IAC5D,IAAI2I,IAAI,CAAC5I,KAAK,GAAG4I,IAAI,CAAC3I,MAAM,GAAG4I,IAAI,CAAC7I,KAAK,GAAG6I,IAAI,CAAC5I,MAAM,EAAE;MACvD;MACAwI,IAAI,GAAGA,IAAI,CAACK,UAAU,EAAE,CAACC,MAAM,CAACF,IAAI,CAAC7I,KAAK,EAAE6I,IAAI,CAAC5I,MAAM,CAAC;IAC1D,CAAC,MAAM;MACL;MACAyI,IAAI,GAAGA,IAAI,CAACI,UAAU,EAAE,CAACC,MAAM,CAACH,IAAI,CAAC5I,KAAK,EAAE4I,IAAI,CAAC3I,MAAM,CAAC;IAC1D;EACF;EAEA,IAAI,OAAO0I,SAAS,KAAK,QAAQ,IAAIA,SAAS,GAAG,CAAC,IAAIA,SAAS,GAAG,CAAC,EAAE;IACnE,OAAOzG,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAE,4CAA4C,CAAC;EAC5E;EAEA,MAAMmK,IAAI,GAAG,IAAIhI,IAAI,CAACoI,IAAI,CAAC5I,KAAK,EAAE4I,IAAI,CAAC3I,MAAM,EAAE,UAAU,CAAC;EAE1D,MAAM+I,aAAa,GAAG,IAAAC,mBAAU,EAC9BL,IAAI,CAACvJ,IAAI,EACTwJ,IAAI,CAACxJ,IAAI,EACTmJ,IAAI,CAACrG,MAAM,CAAC9C,IAAI,EAChBmJ,IAAI,CAACrG,MAAM,CAACnC,KAAK,EACjBwI,IAAI,CAACrG,MAAM,CAAClC,MAAM,EAClB;IAAE0I;EAAU,CAAC,CACd;EAED,OAAO;IACLO,OAAO,EAAEF,aAAa,IAAIR,IAAI,CAACrG,MAAM,CAACnC,KAAK,GAAGwI,IAAI,CAACrG,MAAM,CAAClC,MAAM,CAAC;IACjEqH,KAAK,EAAEkB;EACT,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACAhI,IAAI,CAACkF,QAAQ,GAAG,UAAU+C,IAAI,EAAEC,IAAI,EAAE;EACpC,MAAMS,KAAK,GAAG,IAAI9D,cAAU,EAAE;EAC9B,MAAM+D,KAAK,GAAGD,KAAK,CAAC7D,OAAO,CAACmD,IAAI,CAAC;EACjC,MAAMY,KAAK,GAAGF,KAAK,CAAC7D,OAAO,CAACoD,IAAI,CAAC;EAEjC,OAAOS,KAAK,CAACzD,QAAQ,CAAC0D,KAAK,EAAEC,KAAK,CAAC;AACrC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA7I,IAAI,CAAC8I,aAAa,GAAG,UAAUF,KAAK,EAAEC,KAAK,EAAE;EAC3C,MAAMF,KAAK,GAAG,IAAI9D,cAAU,EAAE;EAE9B,OAAO8D,KAAK,CAACzD,QAAQ,CAAC0D,KAAK,EAAEC,KAAK,CAAC;AACrC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA7I,IAAI,CAAC+I,SAAS,GAAG,UAAUC,KAAK,EAAEC,KAAK,EAAE;EACvC,MAAM1B,GAAG,GAAIM,CAAC,IAAKnC,IAAI,CAAC6B,GAAG,CAACM,CAAC,EAAE,CAAC,CAAC;EACjC,MAAM;IAAEC;EAAI,CAAC,GAAGpC,IAAI;EACpB,MAAMwD,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EAE5B,IAAIF,KAAK,CAAC5B,CAAC,KAAK,CAAC,IAAI,CAAC4B,KAAK,CAAC5B,CAAC,EAAE;IAC7B4B,KAAK,CAAC5B,CAAC,GAAG,GAAG;EACf;EAEA,IAAI6B,KAAK,CAAC7B,CAAC,KAAK,CAAC,IAAI,CAAC6B,KAAK,CAAC7B,CAAC,EAAE;IAC7B6B,KAAK,CAAC7B,CAAC,GAAG,GAAG;EACf;EAEA,OACE,CAACU,GAAG,CAACP,GAAG,CAACyB,KAAK,CAAC/B,CAAC,GAAGgC,KAAK,CAAChC,CAAC,CAAC,EAAEM,GAAG,CAACyB,KAAK,CAAC/B,CAAC,GAAGgC,KAAK,CAAChC,CAAC,GAAG+B,KAAK,CAAC5B,CAAC,GAAG6B,KAAK,CAAC7B,CAAC,CAAC,CAAC,GACtEU,GAAG,CAACP,GAAG,CAACyB,KAAK,CAAC9B,CAAC,GAAG+B,KAAK,CAAC/B,CAAC,CAAC,EAAEK,GAAG,CAACyB,KAAK,CAAC9B,CAAC,GAAG+B,KAAK,CAAC/B,CAAC,GAAG8B,KAAK,CAAC5B,CAAC,GAAG6B,KAAK,CAAC7B,CAAC,CAAC,CAAC,GACvEU,GAAG,CAACP,GAAG,CAACyB,KAAK,CAAC7B,CAAC,GAAG8B,KAAK,CAAC9B,CAAC,CAAC,EAAEI,GAAG,CAACyB,KAAK,CAAC7B,CAAC,GAAG8B,KAAK,CAAC9B,CAAC,GAAG6B,KAAK,CAAC5B,CAAC,GAAG6B,KAAK,CAAC7B,CAAC,CAAC,CAAC,IACzE8B,MAAM;AAEV,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,YAAY,CAAClI,UAAU,EAAEmI,MAAM,EAAEC,MAAM,EAAE;EACvD,MAAMC,YAAY,GAAG,SAAS,GAAGF,MAAM;EACvC,MAAMG,WAAW,GAAGH,MAAM,CAACI,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,IAAI;EAEnDxJ,IAAI,CAACrC,SAAS,CAACsD,UAAU,CAAC,GAAG,YAAmB;IAC9C,IAAIwI,SAAS;IAAC,mCAD0BtJ,IAAI;MAAJA,IAAI;IAAA;IAE5C,MAAMzB,EAAE,GAAGyB,IAAI,CAACkJ,MAAM,CAAC/L,MAAM,GAAG,CAAC,CAAC;IAClC,MAAMwD,YAAY,GAAG,IAAI;IAEzB,IAAI,OAAOpC,EAAE,KAAK,UAAU,EAAE;MAC5B+K,SAAS,GAAG,YAAmB;QAAA,mCAANtJ,IAAI;UAAJA,IAAI;QAAA;QAC3B,MAAM,CAACvB,GAAG,EAAEC,IAAI,CAAC,GAAGsB,IAAI;QAExB,IAAIvB,GAAG,EAAE;UACPkC,YAAY,CAACK,SAAS,CAACF,UAAU,EAAErC,GAAG,CAAC;QACzC,CAAC,MAAM;UACLkC,YAAY,CAACM,SAAS,CAACH,UAAU,EAAEsI,WAAW,EAAE;YAC9C,CAACtI,UAAU,GAAGpC;UAChB,CAAC,CAAC;QACJ;QAEAH,EAAE,CAACgL,KAAK,CAAC,IAAI,EAAEvJ,IAAI,CAAC;MACtB,CAAC;MAEDA,IAAI,CAACA,IAAI,CAAC7C,MAAM,GAAG,CAAC,CAAC,GAAGmM,SAAS;IACnC,CAAC,MAAM;MACLA,SAAS,GAAG,KAAK;IACnB;IAEA,IAAI,CAACrI,SAAS,CAACH,UAAU,EAAEqI,YAAY,CAAC;IAExC,IAAIK,MAAM;IAEV,IAAI;MACFA,MAAM,GAAGN,MAAM,CAACK,KAAK,CAAC,IAAI,EAAEvJ,IAAI,CAAC;MAEjC,IAAI,CAACsJ,SAAS,EAAE;QACd,IAAI,CAACrI,SAAS,CAACH,UAAU,EAAEsI,WAAW,EAAE;UACtC,CAACtI,UAAU,GAAG0I;QAChB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdA,KAAK,CAAC3I,UAAU,GAAGA,UAAU;MAC7B,IAAI,CAACE,SAAS,CAACF,UAAU,EAAE2I,KAAK,CAAC;IACnC;IAEA,OAAOD,MAAM;EACf,CAAC;EAED3J,IAAI,CAACrC,SAAS,CAACsD,UAAU,GAAG,OAAO,CAAC,GAAGoI,MAAM;AAC/C;;AAEA;AACA;AACA;AACA;AACA;AACAF,YAAY,CAAC,OAAO,EAAE,OAAO,EAAE,UAAUzK,EAAE,EAAE;EAC3C,MAAMmL,KAAK,GAAG,IAAI7J,IAAI,CAAC,IAAI,CAAC;EAE5B,IAAI,IAAAoD,oBAAa,EAAC1E,EAAE,CAAC,EAAE;IACrBA,EAAE,CAACb,IAAI,CAACgM,KAAK,EAAE,IAAI,EAAEA,KAAK,CAAC;EAC7B;EAEA,OAAOA,KAAK;AACd,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACO,SAASC,YAAY,CAAC7I,UAAU,EAAEoI,MAAM,EAAE;EAC/CF,YAAY,CAAClI,UAAU,EAAE,QAAQ,EAAEoI,MAAM,CAAC;AAC5C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACAS,YAAY,CAAC,YAAY,EAAE,UAAUhE,GAAG,EAAEpH,EAAE,EAAE;EAC5C,IAAI,OAAOoH,GAAG,KAAK,QAAQ,EAAE;IAC3B,OAAOpE,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAE,sCAAsC,EAAEa,EAAE,CAAC;EAC1E;EAEA,IAAI,CAAC8C,WAAW,GAAGsE,GAAG;EAEtB,IAAI,IAAA1C,oBAAa,EAAC1E,EAAE,CAAC,EAAE;IACrBA,EAAE,CAACb,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC3B;EAEA,OAAO,IAAI;AACb,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAiM,YAAY,CAAC,MAAM,EAAE,UAAU1E,CAAC,EAAEC,CAAC,EAAE/D,CAAC,EAAEC,CAAC,EAAEwI,CAAC,EAAErL,EAAE,EAAE;EAChD,IAAI,OAAO0G,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE;IAClD,OAAO3D,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAE,yBAAyB,EAAEa,EAAE,CAAC;EAC7D;EAEA,IAAI,OAAO4C,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE;IAClD,OAAOG,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAE,yBAAyB,EAAEa,EAAE,CAAC;EAC7D;EAEA,IAAI,OAAOqL,CAAC,KAAK,UAAU,EAAE;IAC3B,OAAOrI,iBAAU,CAAC7D,IAAI,CAAC,IAAI,EAAE,sBAAsB,EAAEa,EAAE,CAAC;EAC1D;EAEA,MAAMiL,MAAM,GAAG,IAAAK,WAAI,EAAC,IAAI,EAAE5E,CAAC,EAAEC,CAAC,EAAE/D,CAAC,EAAEC,CAAC,EAAEwI,CAAC,CAAC;EAExC,IAAI,IAAA3G,oBAAa,EAAC1E,EAAE,CAAC,EAAE;IACrBA,EAAE,CAACb,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE8L,MAAM,CAAC;EAC7B;EAEA,OAAOA,MAAM;AACf,CAAC,CAAC;AAEF,IAAIM,OAAO,CAACC,GAAG,CAACC,WAAW,KAAK,SAAS,EAAE;EACzC;EACA;EACA,IAAIC,EAAE;EAEN,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC/DD,EAAE,GAAGC,MAAM;EACb;EAEA,IAAI,OAAOC,IAAI,KAAK,WAAW,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC3DF,EAAE,GAAGE,IAAI;EACX;EAEAF,EAAE,CAACpK,IAAI,GAAGA,IAAI;EACdoK,EAAE,CAACjM,MAAM,GAAGA,MAAM;AACpB;AAAC,eAIc6B,IAAI;AAAA"}