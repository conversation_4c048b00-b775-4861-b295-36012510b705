@echo off
chcp 65001 >nul
echo ========================================
echo 清理旧版本Python文件
echo ========================================
echo.

echo 正在停止所有Python进程...
taskkill /f /im python.exe 2>nul
taskkill /f /im pythonw.exe 2>nul

echo.
echo 正在删除旧的Python相关文件...

REM 删除Python源文件
if exist "main_app.py" del /f "main_app.py"
if exist "main_optimized.py" del /f "main_optimized.py"
if exist "license_generator.py" del /f "license_generator.py"
if exist "license_system.py" del /f "license_system.py"
if exist "license_validator.py" del /f "license_validator.py"
if exist "pdf_converter.py" del /f "pdf_converter.py"
if exist "security_utils.py" del /f "security_utils.py"
if exist "performance_test.py" del /f "performance_test.py"

REM 删除Python打包文件
if exist "build_exe.py" del /f "build_exe.py"
if exist "build_exe_conservative.py" del /f "build_exe_conservative.py"
if exist "build_simple.py" del /f "build_simple.py"
if exist "build_and_test.py" del /f "build_and_test.py"
if exist "测试打包环境.py" del /f "测试打包环境.py"

REM 删除spec文件
if exist "*.spec" del /f "*.spec"

REM 删除Python依赖文件
if exist "requirements.txt" del /f "requirements.txt"

REM 删除Python批处理文件
if exist "start_app.bat" del /f "start_app.bat"
if exist "start_generator.bat" del /f "start_generator.bat"
if exist "打包exe.bat" del /f "打包exe.bat"
if exist "安装依赖.bat" del /f "安装依赖.bat"
if exist "install_drag_drop.bat" del /f "install_drag_drop.bat"

REM 删除Python构建目录
if exist "build" rmdir /s /q "build"

REM 删除Python数据文件
if exist "license.dat" del /f "license.dat"
if exist "last_time.dat" del /f "last_time.dat"

REM 删除旧的文档文件
if exist "修复说明.md" del /f "修复说明.md"
if exist "优化总结.md" del /f "优化总结.md"
if exist "exe打包系统报告.md" del /f "exe打包系统报告.md"
if exist "右上角转换按钮优化报告.md" del /f "右上角转换按钮优化报告.md"
if exist "响应式布局优化报告.md" del /f "响应式布局优化报告.md"
if exist "完美完成报告.md" del /f "完美完成报告.md"
if exist "开发者信息集成报告.md" del /f "开发者信息集成报告.md"
if exist "性能优化修复说明.md" del /f "性能优化修复说明.md"
if exist "打包成功报告.md" del /f "打包成功报告.md"
if exist "打包说明.md" del /f "打包说明.md"
if exist "拖拽功能修复报告.md" del /f "拖拽功能修复报告.md"
if exist "最终修复总结.md" del /f "最终修复总结.md"
if exist "最终成功总结.md" del /f "最终成功总结.md"
if exist "现代化界面设计说明.md" del /f "现代化界面设计说明.md"
if exist "界面优化报告.md" del /f "界面优化报告.md"
if exist "真正成功完成报告.md" del /f "真正成功完成报告.md"
if exist "项目优化完成报告.md" del /f "项目优化完成报告.md"
if exist "项目总结.md" del /f "项目总结.md"
if exist "项目成功完成报告.md" del /f "项目成功完成报告.md"

echo.
echo ========================================
echo 清理完成！
echo ========================================
echo.
echo 现在请使用新的Electron版本：
echo.
echo 主程序：dist\图片转PDF工具.exe
echo 或者：  release\图片转PDF工具.exe
echo.
echo 授权生成器：dist\授权码生成器.exe
echo 或者：      release\授权码生成器.exe
echo.
echo ========================================
pause
