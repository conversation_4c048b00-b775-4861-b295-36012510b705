const { app, BrowserWindow, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const fs = require('fs');
const { PDFConverter } = require('./services/pdf-converter');
const { LicenseManager } = require('./services/license-manager');
// const { SecurityGuard } = require('./services/security-guard'); // 暂时禁用安全模块

class ImageToPDFApp {
    constructor() {
        this.mainWindow = null;
        this.pdfConverter = new PDFConverter();
        this.licenseManager = new LicenseManager();
        // this.securityGuard = new SecurityGuard(); // 暂时禁用安全模块
        this.isReady = false;
    }

    async initialize() {
        // 等待Electron准备就绪
        await app.whenReady();

        // 先设置IPC处理器
        this.setupIpcHandlers();

        // 启动安全检查（后台）
        // this.securityGuard.startMonitoring(); // 暂时禁用安全模块

        // 创建主窗口
        await this.createMainWindow();

        this.isReady = true;
        console.log('应用初始化完成');
    }

    async createMainWindow() {
        this.mainWindow = new BrowserWindow({
            width: 1200,
            height: 800,
            minWidth: 800,
            minHeight: 600,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                preload: path.join(__dirname, 'preload.js')
            },
            // icon: path.join(__dirname, '../assets/icon.png'), // 暂时移除图标
            show: false, // 先不显示，等加载完成
            titleBarStyle: 'default',
            autoHideMenuBar: true
        });

        // 加载主页面
        await this.mainWindow.loadFile(path.join(__dirname, 'renderer/index.html'));

        // 窗口加载完成后显示
        this.mainWindow.once('ready-to-show', () => {
            this.mainWindow.show();
            
            // 检查授权状态
            this.checkLicenseStatus();
        });

        // 窗口关闭事件
        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });

        // 开发模式下打开开发者工具
        if (process.argv.includes('--dev')) {
            this.mainWindow.webContents.openDevTools();
        }
    }

    setupIpcHandlers() {
        // 文件选择
        ipcMain.handle('select-images', async () => {
            const result = await dialog.showOpenDialog(this.mainWindow, {
                title: '选择图片文件',
                filters: [
                    { name: '图片文件', extensions: ['jpg', 'jpeg', 'png', 'bmp', 'gif', 'tiff', 'webp'] },
                    { name: '所有文件', extensions: ['*'] }
                ],
                properties: ['openFile', 'multiSelections']
            });
            
            return result.canceled ? [] : result.filePaths;
        });

        // PDF保存位置选择
        ipcMain.handle('select-save-location', async () => {
            const result = await dialog.showSaveDialog(this.mainWindow, {
                title: '保存PDF文件',
                defaultPath: 'converted.pdf',
                filters: [
                    { name: 'PDF文件', extensions: ['pdf'] },
                    { name: '所有文件', extensions: ['*'] }
                ]
            });
            
            return result.canceled ? null : result.filePath;
        });

        // 图片转PDF
        ipcMain.handle('convert-to-pdf', async (event, { imagePaths, outputPath, options }) => {
            try {
                // 检查授权
                const licenseValid = await this.licenseManager.validateLicense();
                if (!licenseValid) {
                    throw new Error('授权验证失败，请检查授权状态');
                }

                // 执行转换
                const result = await this.pdfConverter.convertImagesToPDF(
                    imagePaths, 
                    outputPath, 
                    options,
                    (progress) => {
                        // 发送进度更新
                        this.mainWindow.webContents.send('conversion-progress', progress);
                    }
                );

                return { success: true, outputPath };
            } catch (error) {
                console.error('转换失败:', error);
                return { success: false, error: error.message };
            }
        });

        // 获取机器码
        ipcMain.handle('get-machine-code', async () => {
            return this.licenseManager.getMachineCode();
        });

        // 验证授权码
        ipcMain.handle('validate-license-key', async (event, licenseKey) => {
            try {
                const result = await this.licenseManager.activateLicense(licenseKey);
                return { success: true, data: result };
            } catch (error) {
                return { success: false, error: error.message };
            }
        });

        // 获取授权状态
        ipcMain.handle('get-license-status', async () => {
            return this.licenseManager.getLicenseStatus();
        });

        // 打开外部链接
        ipcMain.handle('open-external', async (event, url) => {
            shell.openExternal(url);
        });

        // 显示消息框
        ipcMain.handle('show-message', async (event, { type, title, message }) => {
            const options = {
                type: type || 'info',
                title: title || '提示',
                message: message || '',
                buttons: ['确定']
            };
            
            return dialog.showMessageBox(this.mainWindow, options);
        });

        // 获取应用信息
        ipcMain.handle('get-app-info', async () => {
            return {
                name: app.getName(),
                version: app.getVersion(),
                author: 'zz (TTM自控工程师)',
                contact: '17386047989'
            };
        });
    }

    async checkLicenseStatus() {
        try {
            const status = await this.licenseManager.getLicenseStatus();
            
            if (!status.valid) {
                // 显示授权对话框
                this.mainWindow.webContents.send('show-license-dialog', {
                    machineCode: await this.licenseManager.getMachineCode(),
                    message: status.message || '需要激活授权'
                });
            }
        } catch (error) {
            console.error('检查授权状态失败:', error);
        }
    }
}

// 应用事件处理
app.whenReady().then(async () => {
    const appInstance = new ImageToPDFApp();
    await appInstance.initialize();
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', async () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        const appInstance = new ImageToPDFApp();
        await appInstance.initialize();
    }
});

// 防止多实例运行
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
    app.quit();
} else {
    app.on('second-instance', () => {
        // 如果用户试图打开另一个实例，聚焦到现有窗口
        if (this.mainWindow) {
            if (this.mainWindow.isMinimized()) this.mainWindow.restore();
            this.mainWindow.focus();
        }
    });
}

// 安全设置
app.on('web-contents-created', (event, contents) => {
    contents.on('new-window', (event, navigationUrl) => {
        event.preventDefault();
        shell.openExternal(navigationUrl);
    });
});

module.exports = { ImageToPDFApp };
