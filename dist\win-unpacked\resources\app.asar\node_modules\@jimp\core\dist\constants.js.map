{"version": 3, "file": "constants.js", "names": ["AUTO", "HORIZONTAL_ALIGN_LEFT", "HORIZONTAL_ALIGN_CENTER", "HORIZONTAL_ALIGN_RIGHT", "VERTICAL_ALIGN_TOP", "VERTICAL_ALIGN_MIDDLE", "VERTICAL_ALIGN_BOTTOM", "BLEND_SOURCE_OVER", "BLEND_DESTINATION_OVER", "BLEND_MULTIPLY", "BLEND_ADD", "BLEND_SCREEN", "BLEND_OVERLAY", "BLEND_DARKEN", "BLEND_LIGHTEN", "BLEND_HARDLIGHT", "BLEND_DIFFERENCE", "BLEND_EXCLUSION", "EDGE_EXTEND", "EDGE_WRAP", "EDGE_CROP"], "sources": ["../src/constants.js"], "sourcesContent": ["// used to auto resizing etc.\nexport const AUTO = -1;\n\n// Align modes for cover, contain, bit masks\nexport const HORIZONTAL_ALIGN_LEFT = 1;\nexport const HORIZONTAL_ALIGN_CENTER = 2;\nexport const HORIZONTAL_ALIGN_RIGHT = 4;\n\nexport const VERTICAL_ALIGN_TOP = 8;\nexport const VERTICAL_ALIGN_MIDDLE = 16;\nexport const VERTICAL_ALIGN_BOTTOM = 32;\n\n// blend modes\nexport const BLEND_SOURCE_OVER = \"srcOver\";\nexport const BLEND_DESTINATION_OVER = \"dstOver\";\nexport const BLEND_MULTIPLY = \"multiply\";\nexport const BLEND_ADD = \"add\";\nexport const BLEND_SCREEN = \"screen\";\nexport const BLEND_OVERLAY = \"overlay\";\nexport const BLEND_DARKEN = \"darken\";\nexport const BLEND_LIGHTEN = \"lighten\";\nexport const BLEND_HARDLIGHT = \"hardLight\";\nexport const BLEND_DIFFERENCE = \"difference\";\nexport const BLEND_EXCLUSION = \"exclusion\";\n\n// Edge Handling\nexport const EDGE_EXTEND = 1;\nexport const EDGE_WRAP = 2;\nexport const EDGE_CROP = 3;\n"], "mappings": ";;;;;;AAAA;AACO,MAAMA,IAAI,GAAG,CAAC,CAAC;;AAEtB;AAAA;AACO,MAAMC,qBAAqB,GAAG,CAAC;AAAC;AAChC,MAAMC,uBAAuB,GAAG,CAAC;AAAC;AAClC,MAAMC,sBAAsB,GAAG,CAAC;AAAC;AAEjC,MAAMC,kBAAkB,GAAG,CAAC;AAAC;AAC7B,MAAMC,qBAAqB,GAAG,EAAE;AAAC;AACjC,MAAMC,qBAAqB,GAAG,EAAE;;AAEvC;AAAA;AACO,MAAMC,iBAAiB,GAAG,SAAS;AAAC;AACpC,MAAMC,sBAAsB,GAAG,SAAS;AAAC;AACzC,MAAMC,cAAc,GAAG,UAAU;AAAC;AAClC,MAAMC,SAAS,GAAG,KAAK;AAAC;AACxB,MAAMC,YAAY,GAAG,QAAQ;AAAC;AAC9B,MAAMC,aAAa,GAAG,SAAS;AAAC;AAChC,MAAMC,YAAY,GAAG,QAAQ;AAAC;AAC9B,MAAMC,aAAa,GAAG,SAAS;AAAC;AAChC,MAAMC,eAAe,GAAG,WAAW;AAAC;AACpC,MAAMC,gBAAgB,GAAG,YAAY;AAAC;AACtC,MAAMC,eAAe,GAAG,WAAW;;AAE1C;AAAA;AACO,MAAMC,WAAW,GAAG,CAAC;AAAC;AACtB,MAAMC,SAAS,GAAG,CAAC;AAAC;AACpB,MAAMC,SAAS,GAAG,CAAC;AAAC"}