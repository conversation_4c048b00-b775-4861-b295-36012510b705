{"version": 3, "file": "index.js", "names": ["isNodePattern", "dither", "cb", "rgb565Matrix", "scanQuiet", "bitmap", "width", "height", "x", "y", "idx", "thresholdId", "data", "Math", "min", "call", "dither565", "dither16"], "sources": ["../src/index.js"], "sourcesContent": ["import { isNodePattern } from \"@jimp/utils\";\n\n/**\n * Apply a ordered dithering effect\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\nfunction dither(cb) {\n  const rgb565Matrix = [1, 9, 3, 11, 13, 5, 15, 7, 4, 12, 2, 10, 16, 8, 14, 6];\n  this.scanQuiet(\n    0,\n    0,\n    this.bitmap.width,\n    this.bitmap.height,\n    function (x, y, idx) {\n      const thresholdId = ((y & 3) << 2) + (x % 4);\n      const dither = rgb565Matrix[thresholdId];\n      this.bitmap.data[idx] = Math.min(this.bitmap.data[idx] + dither, 0xff);\n      this.bitmap.data[idx + 1] = Math.min(\n        this.bitmap.data[idx + 1] + dither,\n        0xff\n      );\n      this.bitmap.data[idx + 2] = Math.min(\n        this.bitmap.data[idx + 2] + dither,\n        0xff\n      );\n    }\n  );\n\n  if (isNodePattern(cb)) {\n    cb.call(this, null, this);\n  }\n\n  return this;\n}\n\nexport default () => ({\n  dither565: dither,\n  dither16: dither,\n});\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,aAAa;;AAE3C;AACA;AACA;AACA;AACA;AACA,SAASC,MAAM,CAACC,EAAE,EAAE;EAClB,MAAMC,YAAY,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;EAC5E,IAAI,CAACC,SAAS,CACZ,CAAC,EACD,CAAC,EACD,IAAI,CAACC,MAAM,CAACC,KAAK,EACjB,IAAI,CAACD,MAAM,CAACE,MAAM,EAClB,UAAUC,CAAC,EAAEC,CAAC,EAAEC,GAAG,EAAE;IACnB,MAAMC,WAAW,GAAG,CAAC,CAACF,CAAC,GAAG,CAAC,KAAK,CAAC,IAAKD,CAAC,GAAG,CAAE;IAC5C,MAAMP,MAAM,GAAGE,YAAY,CAACQ,WAAW,CAAC;IACxC,IAAI,CAACN,MAAM,CAACO,IAAI,CAACF,GAAG,CAAC,GAAGG,IAAI,CAACC,GAAG,CAAC,IAAI,CAACT,MAAM,CAACO,IAAI,CAACF,GAAG,CAAC,GAAGT,MAAM,EAAE,IAAI,CAAC;IACtE,IAAI,CAACI,MAAM,CAACO,IAAI,CAACF,GAAG,GAAG,CAAC,CAAC,GAAGG,IAAI,CAACC,GAAG,CAClC,IAAI,CAACT,MAAM,CAACO,IAAI,CAACF,GAAG,GAAG,CAAC,CAAC,GAAGT,MAAM,EAClC,IAAI,CACL;IACD,IAAI,CAACI,MAAM,CAACO,IAAI,CAACF,GAAG,GAAG,CAAC,CAAC,GAAGG,IAAI,CAACC,GAAG,CAClC,IAAI,CAACT,MAAM,CAACO,IAAI,CAACF,GAAG,GAAG,CAAC,CAAC,GAAGT,MAAM,EAClC,IAAI,CACL;EACH,CAAC,CACF;EAED,IAAID,aAAa,CAACE,EAAE,CAAC,EAAE;IACrBA,EAAE,CAACa,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC3B;EAEA,OAAO,IAAI;AACb;AAEA,gBAAe,OAAO;EACpBC,SAAS,EAAEf,MAAM;EACjBgB,QAAQ,EAAEhB;AACZ,CAAC,CAAC"}