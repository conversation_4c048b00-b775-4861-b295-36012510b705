{"version": 3, "file": "index.js", "names": ["MIME_TYPE", "mime", "constants", "MIME_JPEG", "decoders", "JPEG", "decode", "encoders", "image", "encode", "bitmap", "_quality", "data", "class", "quality", "n", "cb", "throwError", "call", "Math", "round", "isNodePattern"], "sources": ["../src/index.js"], "sourcesContent": ["import JPEG from \"jpeg-js\";\nimport { throwError, isNodePattern } from \"@jimp/utils\";\n\nconst MIME_TYPE = \"image/jpeg\";\n\nexport default () => ({\n  mime: { [MIME_TYPE]: [\"jpeg\", \"jpg\", \"jpe\"] },\n\n  constants: {\n    MIME_JPEG: MIME_TYPE,\n  },\n\n  decoders: {\n    [MIME_TYPE]: JPEG.decode,\n  },\n\n  encoders: {\n    [MIME_TYPE]: (image) => JPEG.encode(image.bitmap, image._quality).data,\n  },\n\n  class: {\n    // The quality to be used when saving JPEG images\n    _quality: 100,\n    /**\n     * Sets the quality of the image when saving as JPEG format (default is 100)\n     * @param {number} n The quality to use 0-100\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {Jimp} this for chaining of methods\n     */\n    quality(n, cb) {\n      if (typeof n !== \"number\") {\n        return throwError.call(this, \"n must be a number\", cb);\n      }\n\n      if (n < 0 || n > 100) {\n        return throwError.call(this, \"n must be a number 0 - 100\", cb);\n      }\n\n      this._quality = Math.round(n);\n\n      if (isNodePattern(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    },\n  },\n});\n"], "mappings": ";;;;;;AAAA;AACA;AAAwD;AAExD,MAAMA,SAAS,GAAG,YAAY;AAAC,eAEhB,OAAO;EACpBC,IAAI,EAAE;IAAE,CAACD,SAAS,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK;EAAE,CAAC;EAE7CE,SAAS,EAAE;IACTC,SAAS,EAAEH;EACb,CAAC;EAEDI,QAAQ,EAAE;IACR,CAACJ,SAAS,GAAGK,eAAI,CAACC;EACpB,CAAC;EAEDC,QAAQ,EAAE;IACR,CAACP,SAAS,GAAIQ,KAAK,IAAKH,eAAI,CAACI,MAAM,CAACD,KAAK,CAACE,MAAM,EAAEF,KAAK,CAACG,QAAQ,CAAC,CAACC;EACpE,CAAC;EAEDC,KAAK,EAAE;IACL;IACAF,QAAQ,EAAE,GAAG;IACb;AACJ;AACA;AACA;AACA;AACA;IACIG,OAAO,CAACC,CAAC,EAAEC,EAAE,EAAE;MACb,IAAI,OAAOD,CAAC,KAAK,QAAQ,EAAE;QACzB,OAAOE,iBAAU,CAACC,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAEF,EAAE,CAAC;MACxD;MAEA,IAAID,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,GAAG,EAAE;QACpB,OAAOE,iBAAU,CAACC,IAAI,CAAC,IAAI,EAAE,4BAA4B,EAAEF,EAAE,CAAC;MAChE;MAEA,IAAI,CAACL,QAAQ,GAAGQ,IAAI,CAACC,KAAK,CAACL,CAAC,CAAC;MAE7B,IAAI,IAAAM,oBAAa,EAACL,EAAE,CAAC,EAAE;QACrBA,EAAE,CAACE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAC3B;MAEA,OAAO,IAAI;IACb;EACF;AACF,CAAC,CAAC;AAAA;AAAA;AAAA"}