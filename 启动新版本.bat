@echo off
chcp 65001 >nul
echo ========================================
echo 启动图片转PDF工具 - Electron版本
echo ========================================
echo.

echo 检查文件是否存在...

if exist "dist\图片转PDF工具.exe" (
    echo ✅ 找到主程序：dist\图片转PDF工具.exe
    echo.
    echo 正在启动主程序...
    start "" "dist\图片转PDF工具.exe"
    echo.
    echo ✅ 主程序已启动！
) else if exist "release\图片转PDF工具.exe" (
    echo ✅ 找到主程序：release\图片转PDF工具.exe
    echo.
    echo 正在启动主程序...
    start "" "release\图片转PDF工具.exe"
    echo.
    echo ✅ 主程序已启动！
) else (
    echo ❌ 错误：找不到主程序文件！
    echo.
    echo 请确保以下文件存在：
    echo - dist\图片转PDF工具.exe
    echo - 或 release\图片转PDF工具.exe
    echo.
    echo 如果文件不存在，请运行构建命令：
    echo npm run build-win
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo 注意事项：
echo ========================================
echo.
echo 1. 这是全新的Electron版本，不是Python版本
echo 2. 启动速度快（1-2秒），不会出现模块错误
echo 3. 如果遇到问题，请检查是否误运行了旧版本
echo 4. 授权生成器：dist\授权码生成器.exe
echo.
echo ========================================
pause
