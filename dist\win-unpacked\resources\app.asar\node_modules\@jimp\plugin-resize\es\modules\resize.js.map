{"version": 3, "file": "resize.js", "names": ["Resize", "widthOriginal", "heightOriginal", "targetWidth", "targetHeight", "blendAlpha", "interpolationPass", "resizeCallback", "Math", "abs", "floor", "colorChannels", "Boolean", "targetWidthMultipliedByChannels", "originalWidthMultipliedByChannels", "originalHeightMultipliedByChannels", "widthPassResultSize", "finalResultSize", "initialize", "prototype", "configure<PERSON><PERSON><PERSON>", "Error", "resizeWidth", "bypassResizer", "ratioWeightWidthPass", "initializeFirstPassBuffers", "resizeWidthInterpolatedRGBA", "resizeWidthInterpolatedRGB", "resizeWidthRGBA", "resizeWidthRGB", "resizeHeight", "ratioWeightHeightPass", "initializeSecondPassBuffers", "resizeHeightInterpolated", "resizeHeightRGBA", "resizeHeightRGB", "_resizeWidthInterpolatedRGBChannels", "buffer", "fourthChannel", "channelsNum", "ratioWeight", "outputBuffer", "widthBuffer", "weight", "finalOffset", "pixelOffset", "firstWeight", "secondWeight", "targetPosition", "interpolationWidthSourceReadStop", "_resizeWidthRGBChannels", "ratioWeightDivisor", "nextLineOffsetOriginalWidth", "nextLineOffsetTargetWidth", "output", "outputWidthWorkBench", "trustworthyColorsCount", "outputWidthWorkBenchOpaquePixelsCount", "amountToNext", "actualPosition", "currentPosition", "line", "outputOffset", "multiplier", "r", "g", "b", "a", "min", "_resizeHeightRGBChannels", "outputHeightWorkBench", "heightBuffer", "outputHeightWorkBenchOpaquePixelsCount", "caret", "round", "pixelOffsetAccumulated", "pixelOffsetAccumulated2", "interpolationHeightSourceReadStop", "resize", "BILINEARAlgo", "generateFloatBuffer", "generateFloat64Buffer", "generateUint8Buffer", "bufferLength", "Float32Array", "error", "Float64Array", "Uint8Array"], "sources": ["../../src/modules/resize.js"], "sourcesContent": ["// JavaScript Image Resizer (c) 2012 - <PERSON>\n// Released to public domain 29 July 2013: https://github.com/grantgalitz/JS-Image-Resizer/issues/4\n\nfunction Resize(\n  widthOriginal,\n  heightOriginal,\n  targetWidth,\n  targetHeight,\n  blendAlpha,\n  interpolationPass,\n  resizeCallback\n) {\n  this.widthOriginal = Math.abs(Math.floor(widthOriginal) || 0);\n  this.heightOriginal = Math.abs(Math.floor(heightOriginal) || 0);\n  this.targetWidth = Math.abs(Math.floor(targetWidth) || 0);\n  this.targetHeight = Math.abs(Math.floor(targetHeight) || 0);\n  this.colorChannels = blendAlpha ? 4 : 3;\n  this.interpolationPass = Boolean(interpolationPass);\n  this.resizeCallback =\n    typeof resizeCallback === \"function\" ? resizeCallback : function () {};\n\n  this.targetWidthMultipliedByChannels = this.targetWidth * this.colorChannels;\n  this.originalWidthMultipliedByChannels =\n    this.widthOriginal * this.colorChannels;\n  this.originalHeightMultipliedByChannels =\n    this.heightOriginal * this.colorChannels;\n  this.widthPassResultSize =\n    this.targetWidthMultipliedByChannels * this.heightOriginal;\n  this.finalResultSize =\n    this.targetWidthMultipliedByChannels * this.targetHeight;\n  this.initialize();\n}\n\nResize.prototype.initialize = function () {\n  // Perform some checks:\n  if (\n    this.widthOriginal > 0 &&\n    this.heightOriginal > 0 &&\n    this.targetWidth > 0 &&\n    this.targetHeight > 0\n  ) {\n    this.configurePasses();\n  } else {\n    throw new Error(\"Invalid settings specified for the resizer.\");\n  }\n};\n\nResize.prototype.configurePasses = function () {\n  if (this.widthOriginal === this.targetWidth) {\n    // Bypass the width resizer pass:\n    this.resizeWidth = this.bypassResizer;\n  } else {\n    // Setup the width resizer pass:\n    this.ratioWeightWidthPass = this.widthOriginal / this.targetWidth;\n    if (this.ratioWeightWidthPass < 1 && this.interpolationPass) {\n      this.initializeFirstPassBuffers(true);\n      this.resizeWidth =\n        this.colorChannels === 4\n          ? this.resizeWidthInterpolatedRGBA\n          : this.resizeWidthInterpolatedRGB;\n    } else {\n      this.initializeFirstPassBuffers(false);\n      this.resizeWidth =\n        this.colorChannels === 4 ? this.resizeWidthRGBA : this.resizeWidthRGB;\n    }\n  }\n\n  if (this.heightOriginal === this.targetHeight) {\n    // Bypass the height resizer pass:\n    this.resizeHeight = this.bypassResizer;\n  } else {\n    // Setup the height resizer pass:\n    this.ratioWeightHeightPass = this.heightOriginal / this.targetHeight;\n    if (this.ratioWeightHeightPass < 1 && this.interpolationPass) {\n      this.initializeSecondPassBuffers(true);\n      this.resizeHeight = this.resizeHeightInterpolated;\n    } else {\n      this.initializeSecondPassBuffers(false);\n      this.resizeHeight =\n        this.colorChannels === 4 ? this.resizeHeightRGBA : this.resizeHeightRGB;\n    }\n  }\n};\n\nResize.prototype._resizeWidthInterpolatedRGBChannels = function (\n  buffer,\n  fourthChannel\n) {\n  const channelsNum = fourthChannel ? 4 : 3;\n  const ratioWeight = this.ratioWeightWidthPass;\n  const outputBuffer = this.widthBuffer;\n\n  let weight = 0;\n  let finalOffset = 0;\n  let pixelOffset = 0;\n  let firstWeight = 0;\n  let secondWeight = 0;\n  let targetPosition;\n\n  // Handle for only one interpolation input being valid for start calculation:\n  for (\n    targetPosition = 0;\n    weight < 1 / 3;\n    targetPosition += channelsNum, weight += ratioWeight\n  ) {\n    for (\n      finalOffset = targetPosition, pixelOffset = 0;\n      finalOffset < this.widthPassResultSize;\n      pixelOffset += this.originalWidthMultipliedByChannels,\n        finalOffset += this.targetWidthMultipliedByChannels\n    ) {\n      outputBuffer[finalOffset] = buffer[pixelOffset];\n      outputBuffer[finalOffset + 1] = buffer[pixelOffset + 1];\n      outputBuffer[finalOffset + 2] = buffer[pixelOffset + 2];\n      if (fourthChannel)\n        outputBuffer[finalOffset + 3] = buffer[pixelOffset + 3];\n    }\n  }\n\n  // Adjust for overshoot of the last pass's counter:\n  weight -= 1 / 3;\n  let interpolationWidthSourceReadStop;\n\n  for (\n    interpolationWidthSourceReadStop = this.widthOriginal - 1;\n    weight < interpolationWidthSourceReadStop;\n    targetPosition += channelsNum, weight += ratioWeight\n  ) {\n    // Calculate weightings:\n    secondWeight = weight % 1;\n    firstWeight = 1 - secondWeight;\n    // Interpolate:\n    for (\n      finalOffset = targetPosition,\n        pixelOffset = Math.floor(weight) * channelsNum;\n      finalOffset < this.widthPassResultSize;\n      pixelOffset += this.originalWidthMultipliedByChannels,\n        finalOffset += this.targetWidthMultipliedByChannels\n    ) {\n      outputBuffer[finalOffset + 0] =\n        buffer[pixelOffset + 0] * firstWeight +\n        buffer[pixelOffset + channelsNum + 0] * secondWeight;\n      outputBuffer[finalOffset + 1] =\n        buffer[pixelOffset + 1] * firstWeight +\n        buffer[pixelOffset + channelsNum + 1] * secondWeight;\n      outputBuffer[finalOffset + 2] =\n        buffer[pixelOffset + 2] * firstWeight +\n        buffer[pixelOffset + channelsNum + 2] * secondWeight;\n      if (fourthChannel)\n        outputBuffer[finalOffset + 3] =\n          buffer[pixelOffset + 3] * firstWeight +\n          buffer[pixelOffset + channelsNum + 3] * secondWeight;\n    }\n  }\n\n  // Handle for only one interpolation input being valid for end calculation:\n  for (\n    interpolationWidthSourceReadStop =\n      this.originalWidthMultipliedByChannels - channelsNum;\n    targetPosition < this.targetWidthMultipliedByChannels;\n    targetPosition += channelsNum\n  ) {\n    for (\n      finalOffset = targetPosition,\n        pixelOffset = interpolationWidthSourceReadStop;\n      finalOffset < this.widthPassResultSize;\n      pixelOffset += this.originalWidthMultipliedByChannels,\n        finalOffset += this.targetWidthMultipliedByChannels\n    ) {\n      outputBuffer[finalOffset] = buffer[pixelOffset];\n      outputBuffer[finalOffset + 1] = buffer[pixelOffset + 1];\n      outputBuffer[finalOffset + 2] = buffer[pixelOffset + 2];\n      if (fourthChannel)\n        outputBuffer[finalOffset + 3] = buffer[pixelOffset + 3];\n    }\n  }\n\n  return outputBuffer;\n};\n\nResize.prototype._resizeWidthRGBChannels = function (buffer, fourthChannel) {\n  const channelsNum = fourthChannel ? 4 : 3;\n  const ratioWeight = this.ratioWeightWidthPass;\n  const ratioWeightDivisor = 1 / ratioWeight;\n  const nextLineOffsetOriginalWidth =\n    this.originalWidthMultipliedByChannels - channelsNum + 1;\n  const nextLineOffsetTargetWidth =\n    this.targetWidthMultipliedByChannels - channelsNum + 1;\n  const output = this.outputWidthWorkBench;\n  const outputBuffer = this.widthBuffer;\n  const trustworthyColorsCount = this.outputWidthWorkBenchOpaquePixelsCount;\n\n  let weight = 0;\n  let amountToNext = 0;\n  let actualPosition = 0;\n  let currentPosition = 0;\n  let line = 0;\n  let pixelOffset = 0;\n  let outputOffset = 0;\n  let multiplier = 1;\n  let r = 0;\n  let g = 0;\n  let b = 0;\n  let a = 0;\n\n  do {\n    for (line = 0; line < this.originalHeightMultipliedByChannels; ) {\n      output[line++] = 0;\n      output[line++] = 0;\n      output[line++] = 0;\n      if (fourthChannel) {\n        output[line++] = 0;\n        trustworthyColorsCount[line / channelsNum - 1] = 0;\n      }\n    }\n\n    weight = ratioWeight;\n\n    do {\n      amountToNext = 1 + actualPosition - currentPosition;\n      multiplier = Math.min(weight, amountToNext);\n      for (\n        line = 0, pixelOffset = actualPosition;\n        line < this.originalHeightMultipliedByChannels;\n        pixelOffset += nextLineOffsetOriginalWidth\n      ) {\n        r = buffer[pixelOffset];\n        g = buffer[++pixelOffset];\n        b = buffer[++pixelOffset];\n        a = fourthChannel ? buffer[++pixelOffset] : 255;\n        // Ignore RGB values if pixel is completely transparent\n        output[line++] += (a ? r : 0) * multiplier;\n        output[line++] += (a ? g : 0) * multiplier;\n        output[line++] += (a ? b : 0) * multiplier;\n        if (fourthChannel) {\n          output[line++] += a * multiplier;\n          trustworthyColorsCount[line / channelsNum - 1] += a ? multiplier : 0;\n        }\n      }\n\n      if (weight >= amountToNext) {\n        actualPosition += channelsNum;\n        currentPosition = actualPosition;\n        weight -= amountToNext;\n      } else {\n        currentPosition += weight;\n        break;\n      }\n    } while (\n      weight > 0 &&\n      actualPosition < this.originalWidthMultipliedByChannels\n    );\n\n    for (\n      line = 0, pixelOffset = outputOffset;\n      line < this.originalHeightMultipliedByChannels;\n      pixelOffset += nextLineOffsetTargetWidth\n    ) {\n      weight = fourthChannel ? trustworthyColorsCount[line / channelsNum] : 1;\n      multiplier = fourthChannel\n        ? weight\n          ? 1 / weight\n          : 0\n        : ratioWeightDivisor;\n      outputBuffer[pixelOffset] = output[line++] * multiplier;\n      outputBuffer[++pixelOffset] = output[line++] * multiplier;\n      outputBuffer[++pixelOffset] = output[line++] * multiplier;\n      if (fourthChannel)\n        outputBuffer[++pixelOffset] = output[line++] * ratioWeightDivisor;\n    }\n\n    outputOffset += channelsNum;\n  } while (outputOffset < this.targetWidthMultipliedByChannels);\n\n  return outputBuffer;\n};\n\nResize.prototype._resizeHeightRGBChannels = function (buffer, fourthChannel) {\n  const ratioWeight = this.ratioWeightHeightPass;\n  const ratioWeightDivisor = 1 / ratioWeight;\n  const output = this.outputHeightWorkBench;\n  const outputBuffer = this.heightBuffer;\n  const trustworthyColorsCount = this.outputHeightWorkBenchOpaquePixelsCount;\n\n  let weight = 0;\n  let amountToNext = 0;\n  let actualPosition = 0;\n  let currentPosition = 0;\n  let pixelOffset = 0;\n  let outputOffset = 0;\n  let caret = 0;\n  let multiplier = 1;\n  let r = 0;\n  let g = 0;\n  let b = 0;\n  let a = 0;\n\n  do {\n    for (\n      pixelOffset = 0;\n      pixelOffset < this.targetWidthMultipliedByChannels;\n\n    ) {\n      output[pixelOffset++] = 0;\n      output[pixelOffset++] = 0;\n      output[pixelOffset++] = 0;\n\n      if (fourthChannel) {\n        output[pixelOffset++] = 0;\n        trustworthyColorsCount[pixelOffset / 4 - 1] = 0;\n      }\n    }\n\n    weight = ratioWeight;\n\n    do {\n      amountToNext = 1 + actualPosition - currentPosition;\n      multiplier = Math.min(weight, amountToNext);\n      caret = actualPosition;\n\n      for (\n        pixelOffset = 0;\n        pixelOffset < this.targetWidthMultipliedByChannels;\n\n      ) {\n        r = buffer[caret++];\n        g = buffer[caret++];\n        b = buffer[caret++];\n        a = fourthChannel ? buffer[caret++] : 255;\n        // Ignore RGB values if pixel is completely transparent\n        output[pixelOffset++] += (a ? r : 0) * multiplier;\n        output[pixelOffset++] += (a ? g : 0) * multiplier;\n        output[pixelOffset++] += (a ? b : 0) * multiplier;\n\n        if (fourthChannel) {\n          output[pixelOffset++] += a * multiplier;\n          trustworthyColorsCount[pixelOffset / 4 - 1] += a ? multiplier : 0;\n        }\n      }\n\n      if (weight >= amountToNext) {\n        actualPosition = caret;\n        currentPosition = actualPosition;\n        weight -= amountToNext;\n      } else {\n        currentPosition += weight;\n        break;\n      }\n    } while (weight > 0 && actualPosition < this.widthPassResultSize);\n\n    for (\n      pixelOffset = 0;\n      pixelOffset < this.targetWidthMultipliedByChannels;\n\n    ) {\n      weight = fourthChannel ? trustworthyColorsCount[pixelOffset / 4] : 1;\n      multiplier = fourthChannel\n        ? weight\n          ? 1 / weight\n          : 0\n        : ratioWeightDivisor;\n      outputBuffer[outputOffset++] = Math.round(\n        output[pixelOffset++] * multiplier\n      );\n      outputBuffer[outputOffset++] = Math.round(\n        output[pixelOffset++] * multiplier\n      );\n      outputBuffer[outputOffset++] = Math.round(\n        output[pixelOffset++] * multiplier\n      );\n\n      if (fourthChannel) {\n        outputBuffer[outputOffset++] = Math.round(\n          output[pixelOffset++] * ratioWeightDivisor\n        );\n      }\n    }\n  } while (outputOffset < this.finalResultSize);\n\n  return outputBuffer;\n};\n\nResize.prototype.resizeWidthInterpolatedRGB = function (buffer) {\n  return this._resizeWidthInterpolatedRGBChannels(buffer, false);\n};\n\nResize.prototype.resizeWidthInterpolatedRGBA = function (buffer) {\n  return this._resizeWidthInterpolatedRGBChannels(buffer, true);\n};\n\nResize.prototype.resizeWidthRGB = function (buffer) {\n  return this._resizeWidthRGBChannels(buffer, false);\n};\n\nResize.prototype.resizeWidthRGBA = function (buffer) {\n  return this._resizeWidthRGBChannels(buffer, true);\n};\n\nResize.prototype.resizeHeightInterpolated = function (buffer) {\n  const ratioWeight = this.ratioWeightHeightPass;\n  const outputBuffer = this.heightBuffer;\n\n  let weight = 0;\n  let finalOffset = 0;\n  let pixelOffset = 0;\n  let pixelOffsetAccumulated = 0;\n  let pixelOffsetAccumulated2 = 0;\n  let firstWeight = 0;\n  let secondWeight = 0;\n  let interpolationHeightSourceReadStop;\n\n  // Handle for only one interpolation input being valid for start calculation:\n  for (; weight < 1 / 3; weight += ratioWeight) {\n    for (\n      pixelOffset = 0;\n      pixelOffset < this.targetWidthMultipliedByChannels;\n\n    ) {\n      outputBuffer[finalOffset++] = Math.round(buffer[pixelOffset++]);\n    }\n  }\n\n  // Adjust for overshoot of the last pass's counter:\n  weight -= 1 / 3;\n\n  for (\n    interpolationHeightSourceReadStop = this.heightOriginal - 1;\n    weight < interpolationHeightSourceReadStop;\n    weight += ratioWeight\n  ) {\n    // Calculate weightings:\n    secondWeight = weight % 1;\n    firstWeight = 1 - secondWeight;\n    // Interpolate:\n    pixelOffsetAccumulated =\n      Math.floor(weight) * this.targetWidthMultipliedByChannels;\n    pixelOffsetAccumulated2 =\n      pixelOffsetAccumulated + this.targetWidthMultipliedByChannels;\n    for (\n      pixelOffset = 0;\n      pixelOffset < this.targetWidthMultipliedByChannels;\n      ++pixelOffset\n    ) {\n      outputBuffer[finalOffset++] = Math.round(\n        buffer[pixelOffsetAccumulated++] * firstWeight +\n          buffer[pixelOffsetAccumulated2++] * secondWeight\n      );\n    }\n  }\n\n  // Handle for only one interpolation input being valid for end calculation:\n  while (finalOffset < this.finalResultSize) {\n    for (\n      pixelOffset = 0,\n        pixelOffsetAccumulated =\n          interpolationHeightSourceReadStop *\n          this.targetWidthMultipliedByChannels;\n      pixelOffset < this.targetWidthMultipliedByChannels;\n      ++pixelOffset\n    ) {\n      outputBuffer[finalOffset++] = Math.round(\n        buffer[pixelOffsetAccumulated++]\n      );\n    }\n  }\n\n  return outputBuffer;\n};\n\nResize.prototype.resizeHeightRGB = function (buffer) {\n  return this._resizeHeightRGBChannels(buffer, false);\n};\n\nResize.prototype.resizeHeightRGBA = function (buffer) {\n  return this._resizeHeightRGBChannels(buffer, true);\n};\n\nResize.prototype.resize = function (buffer) {\n  this.resizeCallback(this.resizeHeight(this.resizeWidth(buffer)));\n};\n\nResize.prototype.bypassResizer = function (buffer) {\n  // Just return the buffer passed:\n  return buffer;\n};\n\nResize.prototype.initializeFirstPassBuffers = function (BILINEARAlgo) {\n  // Initialize the internal width pass buffers:\n  this.widthBuffer = this.generateFloatBuffer(this.widthPassResultSize);\n\n  if (!BILINEARAlgo) {\n    this.outputWidthWorkBench = this.generateFloatBuffer(\n      this.originalHeightMultipliedByChannels\n    );\n\n    if (this.colorChannels > 3) {\n      this.outputWidthWorkBenchOpaquePixelsCount = this.generateFloat64Buffer(\n        this.heightOriginal\n      );\n    }\n  }\n};\n\nResize.prototype.initializeSecondPassBuffers = function (BILINEARAlgo) {\n  // Initialize the internal height pass buffers:\n  this.heightBuffer = this.generateUint8Buffer(this.finalResultSize);\n\n  if (!BILINEARAlgo) {\n    this.outputHeightWorkBench = this.generateFloatBuffer(\n      this.targetWidthMultipliedByChannels\n    );\n\n    if (this.colorChannels > 3) {\n      this.outputHeightWorkBenchOpaquePixelsCount = this.generateFloat64Buffer(\n        this.targetWidth\n      );\n    }\n  }\n};\n\nResize.prototype.generateFloatBuffer = function (bufferLength) {\n  // Generate a float32 typed array buffer:\n  try {\n    return new Float32Array(bufferLength);\n  } catch (error) {\n    return [];\n  }\n};\n\nResize.prototype.generateFloat64Buffer = function (bufferLength) {\n  // Generate a float64 typed array buffer:\n  try {\n    return new Float64Array(bufferLength);\n  } catch (error) {\n    return [];\n  }\n};\n\nResize.prototype.generateUint8Buffer = function (bufferLength) {\n  // Generate a uint8 typed array buffer:\n  try {\n    return new Uint8Array(bufferLength);\n  } catch (error) {\n    return [];\n  }\n};\n\nexport default Resize;\n"], "mappings": "AAAA;AACA;;AAEA,SAASA,MAAM,CACbC,aAAa,EACbC,cAAc,EACdC,WAAW,EACXC,YAAY,EACZC,UAAU,EACVC,iBAAiB,EACjBC,cAAc,EACd;EACA,IAAI,CAACN,aAAa,GAAGO,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,KAAK,CAACT,aAAa,CAAC,IAAI,CAAC,CAAC;EAC7D,IAAI,CAACC,cAAc,GAAGM,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,KAAK,CAACR,cAAc,CAAC,IAAI,CAAC,CAAC;EAC/D,IAAI,CAACC,WAAW,GAAGK,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,KAAK,CAACP,WAAW,CAAC,IAAI,CAAC,CAAC;EACzD,IAAI,CAACC,YAAY,GAAGI,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,KAAK,CAACN,YAAY,CAAC,IAAI,CAAC,CAAC;EAC3D,IAAI,CAACO,aAAa,GAAGN,UAAU,GAAG,CAAC,GAAG,CAAC;EACvC,IAAI,CAACC,iBAAiB,GAAGM,OAAO,CAACN,iBAAiB,CAAC;EACnD,IAAI,CAACC,cAAc,GACjB,OAAOA,cAAc,KAAK,UAAU,GAAGA,cAAc,GAAG,YAAY,CAAC,CAAC;EAExE,IAAI,CAACM,+BAA+B,GAAG,IAAI,CAACV,WAAW,GAAG,IAAI,CAACQ,aAAa;EAC5E,IAAI,CAACG,iCAAiC,GACpC,IAAI,CAACb,aAAa,GAAG,IAAI,CAACU,aAAa;EACzC,IAAI,CAACI,kCAAkC,GACrC,IAAI,CAACb,cAAc,GAAG,IAAI,CAACS,aAAa;EAC1C,IAAI,CAACK,mBAAmB,GACtB,IAAI,CAACH,+BAA+B,GAAG,IAAI,CAACX,cAAc;EAC5D,IAAI,CAACe,eAAe,GAClB,IAAI,CAACJ,+BAA+B,GAAG,IAAI,CAACT,YAAY;EAC1D,IAAI,CAACc,UAAU,EAAE;AACnB;AAEAlB,MAAM,CAACmB,SAAS,CAACD,UAAU,GAAG,YAAY;EACxC;EACA,IACE,IAAI,CAACjB,aAAa,GAAG,CAAC,IACtB,IAAI,CAACC,cAAc,GAAG,CAAC,IACvB,IAAI,CAACC,WAAW,GAAG,CAAC,IACpB,IAAI,CAACC,YAAY,GAAG,CAAC,EACrB;IACA,IAAI,CAACgB,eAAe,EAAE;EACxB,CAAC,MAAM;IACL,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;AACF,CAAC;AAEDrB,MAAM,CAACmB,SAAS,CAACC,eAAe,GAAG,YAAY;EAC7C,IAAI,IAAI,CAACnB,aAAa,KAAK,IAAI,CAACE,WAAW,EAAE;IAC3C;IACA,IAAI,CAACmB,WAAW,GAAG,IAAI,CAACC,aAAa;EACvC,CAAC,MAAM;IACL;IACA,IAAI,CAACC,oBAAoB,GAAG,IAAI,CAACvB,aAAa,GAAG,IAAI,CAACE,WAAW;IACjE,IAAI,IAAI,CAACqB,oBAAoB,GAAG,CAAC,IAAI,IAAI,CAAClB,iBAAiB,EAAE;MAC3D,IAAI,CAACmB,0BAA0B,CAAC,IAAI,CAAC;MACrC,IAAI,CAACH,WAAW,GACd,IAAI,CAACX,aAAa,KAAK,CAAC,GACpB,IAAI,CAACe,2BAA2B,GAChC,IAAI,CAACC,0BAA0B;IACvC,CAAC,MAAM;MACL,IAAI,CAACF,0BAA0B,CAAC,KAAK,CAAC;MACtC,IAAI,CAACH,WAAW,GACd,IAAI,CAACX,aAAa,KAAK,CAAC,GAAG,IAAI,CAACiB,eAAe,GAAG,IAAI,CAACC,cAAc;IACzE;EACF;EAEA,IAAI,IAAI,CAAC3B,cAAc,KAAK,IAAI,CAACE,YAAY,EAAE;IAC7C;IACA,IAAI,CAAC0B,YAAY,GAAG,IAAI,CAACP,aAAa;EACxC,CAAC,MAAM;IACL;IACA,IAAI,CAACQ,qBAAqB,GAAG,IAAI,CAAC7B,cAAc,GAAG,IAAI,CAACE,YAAY;IACpE,IAAI,IAAI,CAAC2B,qBAAqB,GAAG,CAAC,IAAI,IAAI,CAACzB,iBAAiB,EAAE;MAC5D,IAAI,CAAC0B,2BAA2B,CAAC,IAAI,CAAC;MACtC,IAAI,CAACF,YAAY,GAAG,IAAI,CAACG,wBAAwB;IACnD,CAAC,MAAM;MACL,IAAI,CAACD,2BAA2B,CAAC,KAAK,CAAC;MACvC,IAAI,CAACF,YAAY,GACf,IAAI,CAACnB,aAAa,KAAK,CAAC,GAAG,IAAI,CAACuB,gBAAgB,GAAG,IAAI,CAACC,eAAe;IAC3E;EACF;AACF,CAAC;AAEDnC,MAAM,CAACmB,SAAS,CAACiB,mCAAmC,GAAG,UACrDC,MAAM,EACNC,aAAa,EACb;EACA,MAAMC,WAAW,GAAGD,aAAa,GAAG,CAAC,GAAG,CAAC;EACzC,MAAME,WAAW,GAAG,IAAI,CAAChB,oBAAoB;EAC7C,MAAMiB,YAAY,GAAG,IAAI,CAACC,WAAW;EAErC,IAAIC,MAAM,GAAG,CAAC;EACd,IAAIC,WAAW,GAAG,CAAC;EACnB,IAAIC,WAAW,GAAG,CAAC;EACnB,IAAIC,WAAW,GAAG,CAAC;EACnB,IAAIC,YAAY,GAAG,CAAC;EACpB,IAAIC,cAAc;;EAElB;EACA,KACEA,cAAc,GAAG,CAAC,EAClBL,MAAM,GAAG,CAAC,GAAG,CAAC,EACdK,cAAc,IAAIT,WAAW,EAAEI,MAAM,IAAIH,WAAW,EACpD;IACA,KACEI,WAAW,GAAGI,cAAc,EAAEH,WAAW,GAAG,CAAC,EAC7CD,WAAW,GAAG,IAAI,CAAC5B,mBAAmB,EACtC6B,WAAW,IAAI,IAAI,CAAC/B,iCAAiC,EACnD8B,WAAW,IAAI,IAAI,CAAC/B,+BAA+B,EACrD;MACA4B,YAAY,CAACG,WAAW,CAAC,GAAGP,MAAM,CAACQ,WAAW,CAAC;MAC/CJ,YAAY,CAACG,WAAW,GAAG,CAAC,CAAC,GAAGP,MAAM,CAACQ,WAAW,GAAG,CAAC,CAAC;MACvDJ,YAAY,CAACG,WAAW,GAAG,CAAC,CAAC,GAAGP,MAAM,CAACQ,WAAW,GAAG,CAAC,CAAC;MACvD,IAAIP,aAAa,EACfG,YAAY,CAACG,WAAW,GAAG,CAAC,CAAC,GAAGP,MAAM,CAACQ,WAAW,GAAG,CAAC,CAAC;IAC3D;EACF;;EAEA;EACAF,MAAM,IAAI,CAAC,GAAG,CAAC;EACf,IAAIM,gCAAgC;EAEpC,KACEA,gCAAgC,GAAG,IAAI,CAAChD,aAAa,GAAG,CAAC,EACzD0C,MAAM,GAAGM,gCAAgC,EACzCD,cAAc,IAAIT,WAAW,EAAEI,MAAM,IAAIH,WAAW,EACpD;IACA;IACAO,YAAY,GAAGJ,MAAM,GAAG,CAAC;IACzBG,WAAW,GAAG,CAAC,GAAGC,YAAY;IAC9B;IACA,KACEH,WAAW,GAAGI,cAAc,EAC1BH,WAAW,GAAGrC,IAAI,CAACE,KAAK,CAACiC,MAAM,CAAC,GAAGJ,WAAW,EAChDK,WAAW,GAAG,IAAI,CAAC5B,mBAAmB,EACtC6B,WAAW,IAAI,IAAI,CAAC/B,iCAAiC,EACnD8B,WAAW,IAAI,IAAI,CAAC/B,+BAA+B,EACrD;MACA4B,YAAY,CAACG,WAAW,GAAG,CAAC,CAAC,GAC3BP,MAAM,CAACQ,WAAW,GAAG,CAAC,CAAC,GAAGC,WAAW,GACrCT,MAAM,CAACQ,WAAW,GAAGN,WAAW,GAAG,CAAC,CAAC,GAAGQ,YAAY;MACtDN,YAAY,CAACG,WAAW,GAAG,CAAC,CAAC,GAC3BP,MAAM,CAACQ,WAAW,GAAG,CAAC,CAAC,GAAGC,WAAW,GACrCT,MAAM,CAACQ,WAAW,GAAGN,WAAW,GAAG,CAAC,CAAC,GAAGQ,YAAY;MACtDN,YAAY,CAACG,WAAW,GAAG,CAAC,CAAC,GAC3BP,MAAM,CAACQ,WAAW,GAAG,CAAC,CAAC,GAAGC,WAAW,GACrCT,MAAM,CAACQ,WAAW,GAAGN,WAAW,GAAG,CAAC,CAAC,GAAGQ,YAAY;MACtD,IAAIT,aAAa,EACfG,YAAY,CAACG,WAAW,GAAG,CAAC,CAAC,GAC3BP,MAAM,CAACQ,WAAW,GAAG,CAAC,CAAC,GAAGC,WAAW,GACrCT,MAAM,CAACQ,WAAW,GAAGN,WAAW,GAAG,CAAC,CAAC,GAAGQ,YAAY;IAC1D;EACF;;EAEA;EACA,KACEE,gCAAgC,GAC9B,IAAI,CAACnC,iCAAiC,GAAGyB,WAAW,EACtDS,cAAc,GAAG,IAAI,CAACnC,+BAA+B,EACrDmC,cAAc,IAAIT,WAAW,EAC7B;IACA,KACEK,WAAW,GAAGI,cAAc,EAC1BH,WAAW,GAAGI,gCAAgC,EAChDL,WAAW,GAAG,IAAI,CAAC5B,mBAAmB,EACtC6B,WAAW,IAAI,IAAI,CAAC/B,iCAAiC,EACnD8B,WAAW,IAAI,IAAI,CAAC/B,+BAA+B,EACrD;MACA4B,YAAY,CAACG,WAAW,CAAC,GAAGP,MAAM,CAACQ,WAAW,CAAC;MAC/CJ,YAAY,CAACG,WAAW,GAAG,CAAC,CAAC,GAAGP,MAAM,CAACQ,WAAW,GAAG,CAAC,CAAC;MACvDJ,YAAY,CAACG,WAAW,GAAG,CAAC,CAAC,GAAGP,MAAM,CAACQ,WAAW,GAAG,CAAC,CAAC;MACvD,IAAIP,aAAa,EACfG,YAAY,CAACG,WAAW,GAAG,CAAC,CAAC,GAAGP,MAAM,CAACQ,WAAW,GAAG,CAAC,CAAC;IAC3D;EACF;EAEA,OAAOJ,YAAY;AACrB,CAAC;AAEDzC,MAAM,CAACmB,SAAS,CAAC+B,uBAAuB,GAAG,UAAUb,MAAM,EAAEC,aAAa,EAAE;EAC1E,MAAMC,WAAW,GAAGD,aAAa,GAAG,CAAC,GAAG,CAAC;EACzC,MAAME,WAAW,GAAG,IAAI,CAAChB,oBAAoB;EAC7C,MAAM2B,kBAAkB,GAAG,CAAC,GAAGX,WAAW;EAC1C,MAAMY,2BAA2B,GAC/B,IAAI,CAACtC,iCAAiC,GAAGyB,WAAW,GAAG,CAAC;EAC1D,MAAMc,yBAAyB,GAC7B,IAAI,CAACxC,+BAA+B,GAAG0B,WAAW,GAAG,CAAC;EACxD,MAAMe,MAAM,GAAG,IAAI,CAACC,oBAAoB;EACxC,MAAMd,YAAY,GAAG,IAAI,CAACC,WAAW;EACrC,MAAMc,sBAAsB,GAAG,IAAI,CAACC,qCAAqC;EAEzE,IAAId,MAAM,GAAG,CAAC;EACd,IAAIe,YAAY,GAAG,CAAC;EACpB,IAAIC,cAAc,GAAG,CAAC;EACtB,IAAIC,eAAe,GAAG,CAAC;EACvB,IAAIC,IAAI,GAAG,CAAC;EACZ,IAAIhB,WAAW,GAAG,CAAC;EACnB,IAAIiB,YAAY,GAAG,CAAC;EACpB,IAAIC,UAAU,GAAG,CAAC;EAClB,IAAIC,CAAC,GAAG,CAAC;EACT,IAAIC,CAAC,GAAG,CAAC;EACT,IAAIC,CAAC,GAAG,CAAC;EACT,IAAIC,CAAC,GAAG,CAAC;EAET,GAAG;IACD,KAAKN,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG,IAAI,CAAC9C,kCAAkC,GAAI;MAC/DuC,MAAM,CAACO,IAAI,EAAE,CAAC,GAAG,CAAC;MAClBP,MAAM,CAACO,IAAI,EAAE,CAAC,GAAG,CAAC;MAClBP,MAAM,CAACO,IAAI,EAAE,CAAC,GAAG,CAAC;MAClB,IAAIvB,aAAa,EAAE;QACjBgB,MAAM,CAACO,IAAI,EAAE,CAAC,GAAG,CAAC;QAClBL,sBAAsB,CAACK,IAAI,GAAGtB,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC;MACpD;IACF;IAEAI,MAAM,GAAGH,WAAW;IAEpB,GAAG;MACDkB,YAAY,GAAG,CAAC,GAAGC,cAAc,GAAGC,eAAe;MACnDG,UAAU,GAAGvD,IAAI,CAAC4D,GAAG,CAACzB,MAAM,EAAEe,YAAY,CAAC;MAC3C,KACEG,IAAI,GAAG,CAAC,EAAEhB,WAAW,GAAGc,cAAc,EACtCE,IAAI,GAAG,IAAI,CAAC9C,kCAAkC,EAC9C8B,WAAW,IAAIO,2BAA2B,EAC1C;QACAY,CAAC,GAAG3B,MAAM,CAACQ,WAAW,CAAC;QACvBoB,CAAC,GAAG5B,MAAM,CAAC,EAAEQ,WAAW,CAAC;QACzBqB,CAAC,GAAG7B,MAAM,CAAC,EAAEQ,WAAW,CAAC;QACzBsB,CAAC,GAAG7B,aAAa,GAAGD,MAAM,CAAC,EAAEQ,WAAW,CAAC,GAAG,GAAG;QAC/C;QACAS,MAAM,CAACO,IAAI,EAAE,CAAC,IAAI,CAACM,CAAC,GAAGH,CAAC,GAAG,CAAC,IAAID,UAAU;QAC1CT,MAAM,CAACO,IAAI,EAAE,CAAC,IAAI,CAACM,CAAC,GAAGF,CAAC,GAAG,CAAC,IAAIF,UAAU;QAC1CT,MAAM,CAACO,IAAI,EAAE,CAAC,IAAI,CAACM,CAAC,GAAGD,CAAC,GAAG,CAAC,IAAIH,UAAU;QAC1C,IAAIzB,aAAa,EAAE;UACjBgB,MAAM,CAACO,IAAI,EAAE,CAAC,IAAIM,CAAC,GAAGJ,UAAU;UAChCP,sBAAsB,CAACK,IAAI,GAAGtB,WAAW,GAAG,CAAC,CAAC,IAAI4B,CAAC,GAAGJ,UAAU,GAAG,CAAC;QACtE;MACF;MAEA,IAAIpB,MAAM,IAAIe,YAAY,EAAE;QAC1BC,cAAc,IAAIpB,WAAW;QAC7BqB,eAAe,GAAGD,cAAc;QAChChB,MAAM,IAAIe,YAAY;MACxB,CAAC,MAAM;QACLE,eAAe,IAAIjB,MAAM;QACzB;MACF;IACF,CAAC,QACCA,MAAM,GAAG,CAAC,IACVgB,cAAc,GAAG,IAAI,CAAC7C,iCAAiC;IAGzD,KACE+C,IAAI,GAAG,CAAC,EAAEhB,WAAW,GAAGiB,YAAY,EACpCD,IAAI,GAAG,IAAI,CAAC9C,kCAAkC,EAC9C8B,WAAW,IAAIQ,yBAAyB,EACxC;MACAV,MAAM,GAAGL,aAAa,GAAGkB,sBAAsB,CAACK,IAAI,GAAGtB,WAAW,CAAC,GAAG,CAAC;MACvEwB,UAAU,GAAGzB,aAAa,GACtBK,MAAM,GACJ,CAAC,GAAGA,MAAM,GACV,CAAC,GACHQ,kBAAkB;MACtBV,YAAY,CAACI,WAAW,CAAC,GAAGS,MAAM,CAACO,IAAI,EAAE,CAAC,GAAGE,UAAU;MACvDtB,YAAY,CAAC,EAAEI,WAAW,CAAC,GAAGS,MAAM,CAACO,IAAI,EAAE,CAAC,GAAGE,UAAU;MACzDtB,YAAY,CAAC,EAAEI,WAAW,CAAC,GAAGS,MAAM,CAACO,IAAI,EAAE,CAAC,GAAGE,UAAU;MACzD,IAAIzB,aAAa,EACfG,YAAY,CAAC,EAAEI,WAAW,CAAC,GAAGS,MAAM,CAACO,IAAI,EAAE,CAAC,GAAGV,kBAAkB;IACrE;IAEAW,YAAY,IAAIvB,WAAW;EAC7B,CAAC,QAAQuB,YAAY,GAAG,IAAI,CAACjD,+BAA+B;EAE5D,OAAO4B,YAAY;AACrB,CAAC;AAEDzC,MAAM,CAACmB,SAAS,CAACkD,wBAAwB,GAAG,UAAUhC,MAAM,EAAEC,aAAa,EAAE;EAC3E,MAAME,WAAW,GAAG,IAAI,CAACT,qBAAqB;EAC9C,MAAMoB,kBAAkB,GAAG,CAAC,GAAGX,WAAW;EAC1C,MAAMc,MAAM,GAAG,IAAI,CAACgB,qBAAqB;EACzC,MAAM7B,YAAY,GAAG,IAAI,CAAC8B,YAAY;EACtC,MAAMf,sBAAsB,GAAG,IAAI,CAACgB,sCAAsC;EAE1E,IAAI7B,MAAM,GAAG,CAAC;EACd,IAAIe,YAAY,GAAG,CAAC;EACpB,IAAIC,cAAc,GAAG,CAAC;EACtB,IAAIC,eAAe,GAAG,CAAC;EACvB,IAAIf,WAAW,GAAG,CAAC;EACnB,IAAIiB,YAAY,GAAG,CAAC;EACpB,IAAIW,KAAK,GAAG,CAAC;EACb,IAAIV,UAAU,GAAG,CAAC;EAClB,IAAIC,CAAC,GAAG,CAAC;EACT,IAAIC,CAAC,GAAG,CAAC;EACT,IAAIC,CAAC,GAAG,CAAC;EACT,IAAIC,CAAC,GAAG,CAAC;EAET,GAAG;IACD,KACEtB,WAAW,GAAG,CAAC,EACfA,WAAW,GAAG,IAAI,CAAChC,+BAA+B,GAElD;MACAyC,MAAM,CAACT,WAAW,EAAE,CAAC,GAAG,CAAC;MACzBS,MAAM,CAACT,WAAW,EAAE,CAAC,GAAG,CAAC;MACzBS,MAAM,CAACT,WAAW,EAAE,CAAC,GAAG,CAAC;MAEzB,IAAIP,aAAa,EAAE;QACjBgB,MAAM,CAACT,WAAW,EAAE,CAAC,GAAG,CAAC;QACzBW,sBAAsB,CAACX,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MACjD;IACF;IAEAF,MAAM,GAAGH,WAAW;IAEpB,GAAG;MACDkB,YAAY,GAAG,CAAC,GAAGC,cAAc,GAAGC,eAAe;MACnDG,UAAU,GAAGvD,IAAI,CAAC4D,GAAG,CAACzB,MAAM,EAAEe,YAAY,CAAC;MAC3Ce,KAAK,GAAGd,cAAc;MAEtB,KACEd,WAAW,GAAG,CAAC,EACfA,WAAW,GAAG,IAAI,CAAChC,+BAA+B,GAElD;QACAmD,CAAC,GAAG3B,MAAM,CAACoC,KAAK,EAAE,CAAC;QACnBR,CAAC,GAAG5B,MAAM,CAACoC,KAAK,EAAE,CAAC;QACnBP,CAAC,GAAG7B,MAAM,CAACoC,KAAK,EAAE,CAAC;QACnBN,CAAC,GAAG7B,aAAa,GAAGD,MAAM,CAACoC,KAAK,EAAE,CAAC,GAAG,GAAG;QACzC;QACAnB,MAAM,CAACT,WAAW,EAAE,CAAC,IAAI,CAACsB,CAAC,GAAGH,CAAC,GAAG,CAAC,IAAID,UAAU;QACjDT,MAAM,CAACT,WAAW,EAAE,CAAC,IAAI,CAACsB,CAAC,GAAGF,CAAC,GAAG,CAAC,IAAIF,UAAU;QACjDT,MAAM,CAACT,WAAW,EAAE,CAAC,IAAI,CAACsB,CAAC,GAAGD,CAAC,GAAG,CAAC,IAAIH,UAAU;QAEjD,IAAIzB,aAAa,EAAE;UACjBgB,MAAM,CAACT,WAAW,EAAE,CAAC,IAAIsB,CAAC,GAAGJ,UAAU;UACvCP,sBAAsB,CAACX,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,IAAIsB,CAAC,GAAGJ,UAAU,GAAG,CAAC;QACnE;MACF;MAEA,IAAIpB,MAAM,IAAIe,YAAY,EAAE;QAC1BC,cAAc,GAAGc,KAAK;QACtBb,eAAe,GAAGD,cAAc;QAChChB,MAAM,IAAIe,YAAY;MACxB,CAAC,MAAM;QACLE,eAAe,IAAIjB,MAAM;QACzB;MACF;IACF,CAAC,QAAQA,MAAM,GAAG,CAAC,IAAIgB,cAAc,GAAG,IAAI,CAAC3C,mBAAmB;IAEhE,KACE6B,WAAW,GAAG,CAAC,EACfA,WAAW,GAAG,IAAI,CAAChC,+BAA+B,GAElD;MACA8B,MAAM,GAAGL,aAAa,GAAGkB,sBAAsB,CAACX,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC;MACpEkB,UAAU,GAAGzB,aAAa,GACtBK,MAAM,GACJ,CAAC,GAAGA,MAAM,GACV,CAAC,GACHQ,kBAAkB;MACtBV,YAAY,CAACqB,YAAY,EAAE,CAAC,GAAGtD,IAAI,CAACkE,KAAK,CACvCpB,MAAM,CAACT,WAAW,EAAE,CAAC,GAAGkB,UAAU,CACnC;MACDtB,YAAY,CAACqB,YAAY,EAAE,CAAC,GAAGtD,IAAI,CAACkE,KAAK,CACvCpB,MAAM,CAACT,WAAW,EAAE,CAAC,GAAGkB,UAAU,CACnC;MACDtB,YAAY,CAACqB,YAAY,EAAE,CAAC,GAAGtD,IAAI,CAACkE,KAAK,CACvCpB,MAAM,CAACT,WAAW,EAAE,CAAC,GAAGkB,UAAU,CACnC;MAED,IAAIzB,aAAa,EAAE;QACjBG,YAAY,CAACqB,YAAY,EAAE,CAAC,GAAGtD,IAAI,CAACkE,KAAK,CACvCpB,MAAM,CAACT,WAAW,EAAE,CAAC,GAAGM,kBAAkB,CAC3C;MACH;IACF;EACF,CAAC,QAAQW,YAAY,GAAG,IAAI,CAAC7C,eAAe;EAE5C,OAAOwB,YAAY;AACrB,CAAC;AAEDzC,MAAM,CAACmB,SAAS,CAACQ,0BAA0B,GAAG,UAAUU,MAAM,EAAE;EAC9D,OAAO,IAAI,CAACD,mCAAmC,CAACC,MAAM,EAAE,KAAK,CAAC;AAChE,CAAC;AAEDrC,MAAM,CAACmB,SAAS,CAACO,2BAA2B,GAAG,UAAUW,MAAM,EAAE;EAC/D,OAAO,IAAI,CAACD,mCAAmC,CAACC,MAAM,EAAE,IAAI,CAAC;AAC/D,CAAC;AAEDrC,MAAM,CAACmB,SAAS,CAACU,cAAc,GAAG,UAAUQ,MAAM,EAAE;EAClD,OAAO,IAAI,CAACa,uBAAuB,CAACb,MAAM,EAAE,KAAK,CAAC;AACpD,CAAC;AAEDrC,MAAM,CAACmB,SAAS,CAACS,eAAe,GAAG,UAAUS,MAAM,EAAE;EACnD,OAAO,IAAI,CAACa,uBAAuB,CAACb,MAAM,EAAE,IAAI,CAAC;AACnD,CAAC;AAEDrC,MAAM,CAACmB,SAAS,CAACc,wBAAwB,GAAG,UAAUI,MAAM,EAAE;EAC5D,MAAMG,WAAW,GAAG,IAAI,CAACT,qBAAqB;EAC9C,MAAMU,YAAY,GAAG,IAAI,CAAC8B,YAAY;EAEtC,IAAI5B,MAAM,GAAG,CAAC;EACd,IAAIC,WAAW,GAAG,CAAC;EACnB,IAAIC,WAAW,GAAG,CAAC;EACnB,IAAI8B,sBAAsB,GAAG,CAAC;EAC9B,IAAIC,uBAAuB,GAAG,CAAC;EAC/B,IAAI9B,WAAW,GAAG,CAAC;EACnB,IAAIC,YAAY,GAAG,CAAC;EACpB,IAAI8B,iCAAiC;;EAErC;EACA,OAAOlC,MAAM,GAAG,CAAC,GAAG,CAAC,EAAEA,MAAM,IAAIH,WAAW,EAAE;IAC5C,KACEK,WAAW,GAAG,CAAC,EACfA,WAAW,GAAG,IAAI,CAAChC,+BAA+B,GAElD;MACA4B,YAAY,CAACG,WAAW,EAAE,CAAC,GAAGpC,IAAI,CAACkE,KAAK,CAACrC,MAAM,CAACQ,WAAW,EAAE,CAAC,CAAC;IACjE;EACF;;EAEA;EACAF,MAAM,IAAI,CAAC,GAAG,CAAC;EAEf,KACEkC,iCAAiC,GAAG,IAAI,CAAC3E,cAAc,GAAG,CAAC,EAC3DyC,MAAM,GAAGkC,iCAAiC,EAC1ClC,MAAM,IAAIH,WAAW,EACrB;IACA;IACAO,YAAY,GAAGJ,MAAM,GAAG,CAAC;IACzBG,WAAW,GAAG,CAAC,GAAGC,YAAY;IAC9B;IACA4B,sBAAsB,GACpBnE,IAAI,CAACE,KAAK,CAACiC,MAAM,CAAC,GAAG,IAAI,CAAC9B,+BAA+B;IAC3D+D,uBAAuB,GACrBD,sBAAsB,GAAG,IAAI,CAAC9D,+BAA+B;IAC/D,KACEgC,WAAW,GAAG,CAAC,EACfA,WAAW,GAAG,IAAI,CAAChC,+BAA+B,EAClD,EAAEgC,WAAW,EACb;MACAJ,YAAY,CAACG,WAAW,EAAE,CAAC,GAAGpC,IAAI,CAACkE,KAAK,CACtCrC,MAAM,CAACsC,sBAAsB,EAAE,CAAC,GAAG7B,WAAW,GAC5CT,MAAM,CAACuC,uBAAuB,EAAE,CAAC,GAAG7B,YAAY,CACnD;IACH;EACF;;EAEA;EACA,OAAOH,WAAW,GAAG,IAAI,CAAC3B,eAAe,EAAE;IACzC,KACE4B,WAAW,GAAG,CAAC,EACb8B,sBAAsB,GACpBE,iCAAiC,GACjC,IAAI,CAAChE,+BAA+B,EACxCgC,WAAW,GAAG,IAAI,CAAChC,+BAA+B,EAClD,EAAEgC,WAAW,EACb;MACAJ,YAAY,CAACG,WAAW,EAAE,CAAC,GAAGpC,IAAI,CAACkE,KAAK,CACtCrC,MAAM,CAACsC,sBAAsB,EAAE,CAAC,CACjC;IACH;EACF;EAEA,OAAOlC,YAAY;AACrB,CAAC;AAEDzC,MAAM,CAACmB,SAAS,CAACgB,eAAe,GAAG,UAAUE,MAAM,EAAE;EACnD,OAAO,IAAI,CAACgC,wBAAwB,CAAChC,MAAM,EAAE,KAAK,CAAC;AACrD,CAAC;AAEDrC,MAAM,CAACmB,SAAS,CAACe,gBAAgB,GAAG,UAAUG,MAAM,EAAE;EACpD,OAAO,IAAI,CAACgC,wBAAwB,CAAChC,MAAM,EAAE,IAAI,CAAC;AACpD,CAAC;AAEDrC,MAAM,CAACmB,SAAS,CAAC2D,MAAM,GAAG,UAAUzC,MAAM,EAAE;EAC1C,IAAI,CAAC9B,cAAc,CAAC,IAAI,CAACuB,YAAY,CAAC,IAAI,CAACR,WAAW,CAACe,MAAM,CAAC,CAAC,CAAC;AAClE,CAAC;AAEDrC,MAAM,CAACmB,SAAS,CAACI,aAAa,GAAG,UAAUc,MAAM,EAAE;EACjD;EACA,OAAOA,MAAM;AACf,CAAC;AAEDrC,MAAM,CAACmB,SAAS,CAACM,0BAA0B,GAAG,UAAUsD,YAAY,EAAE;EACpE;EACA,IAAI,CAACrC,WAAW,GAAG,IAAI,CAACsC,mBAAmB,CAAC,IAAI,CAAChE,mBAAmB,CAAC;EAErE,IAAI,CAAC+D,YAAY,EAAE;IACjB,IAAI,CAACxB,oBAAoB,GAAG,IAAI,CAACyB,mBAAmB,CAClD,IAAI,CAACjE,kCAAkC,CACxC;IAED,IAAI,IAAI,CAACJ,aAAa,GAAG,CAAC,EAAE;MAC1B,IAAI,CAAC8C,qCAAqC,GAAG,IAAI,CAACwB,qBAAqB,CACrE,IAAI,CAAC/E,cAAc,CACpB;IACH;EACF;AACF,CAAC;AAEDF,MAAM,CAACmB,SAAS,CAACa,2BAA2B,GAAG,UAAU+C,YAAY,EAAE;EACrE;EACA,IAAI,CAACR,YAAY,GAAG,IAAI,CAACW,mBAAmB,CAAC,IAAI,CAACjE,eAAe,CAAC;EAElE,IAAI,CAAC8D,YAAY,EAAE;IACjB,IAAI,CAACT,qBAAqB,GAAG,IAAI,CAACU,mBAAmB,CACnD,IAAI,CAACnE,+BAA+B,CACrC;IAED,IAAI,IAAI,CAACF,aAAa,GAAG,CAAC,EAAE;MAC1B,IAAI,CAAC6D,sCAAsC,GAAG,IAAI,CAACS,qBAAqB,CACtE,IAAI,CAAC9E,WAAW,CACjB;IACH;EACF;AACF,CAAC;AAEDH,MAAM,CAACmB,SAAS,CAAC6D,mBAAmB,GAAG,UAAUG,YAAY,EAAE;EAC7D;EACA,IAAI;IACF,OAAO,IAAIC,YAAY,CAACD,YAAY,CAAC;EACvC,CAAC,CAAC,OAAOE,KAAK,EAAE;IACd,OAAO,EAAE;EACX;AACF,CAAC;AAEDrF,MAAM,CAACmB,SAAS,CAAC8D,qBAAqB,GAAG,UAAUE,YAAY,EAAE;EAC/D;EACA,IAAI;IACF,OAAO,IAAIG,YAAY,CAACH,YAAY,CAAC;EACvC,CAAC,CAAC,OAAOE,KAAK,EAAE;IACd,OAAO,EAAE;EACX;AACF,CAAC;AAEDrF,MAAM,CAACmB,SAAS,CAAC+D,mBAAmB,GAAG,UAAUC,YAAY,EAAE;EAC7D;EACA,IAAI;IACF,OAAO,IAAII,UAAU,CAACJ,YAAY,CAAC;EACrC,CAAC,CAAC,OAAOE,KAAK,EAAE;IACd,OAAO,EAAE;EACX;AACF,CAAC;AAED,eAAerF,MAAM"}