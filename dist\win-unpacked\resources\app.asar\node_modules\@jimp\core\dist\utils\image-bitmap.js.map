{"version": 3, "file": "image-bitmap.js", "names": ["getMIMEFromBuffer", "buffer", "path", "fileTypeFromBuffer", "FileType", "fromBuffer", "mime", "MIME", "getType", "getExifOrientation", "img", "_exif", "tags", "Orientation", "getExifOrientationTransformation", "w", "getWidth", "h", "getHeight", "x", "y", "transformBitmap", "width", "height", "transformation", "_data", "bitmap", "data", "_width", "<PERSON><PERSON><PERSON>", "alloc", "length", "_x", "_y", "idx", "_idx", "pixel", "readUInt32BE", "writeUInt32BE", "exifRotate", "swapDimensions", "newWidth", "newHeight", "parseBitmap", "cb", "Error", "_originalMime", "toLowerCase", "getMIME", "constructor", "decoders", "throwError", "call", "error", "EXIFParser", "create", "parse", "compositeBitmapOverBackground", "<PERSON><PERSON>", "image", "_background", "composite", "<PERSON><PERSON><PERSON><PERSON>", "constants", "AUTO", "_rgba", "has<PERSON><PERSON><PERSON>", "from", "encoders", "Promise", "then", "buff", "getBufferAsync", "promisify"], "sources": ["../../src/utils/image-bitmap.js"], "sourcesContent": ["import FileType from \"file-type\";\n\nimport <PERSON><PERSON><PERSON><PERSON>ars<PERSON> from \"exif-parser\";\nimport { throwError } from \"@jimp/utils\";\n\nimport * as constants from \"../constants\";\nimport * as MIME from \"./mime\";\nimport promisify from \"./promisify\";\n\nasync function getMIMEFromBuffer(buffer, path) {\n  const fileTypeFromBuffer = await FileType.fromBuffer(buffer);\n\n  if (fileTypeFromBuffer) {\n    // If fileType returns something for buffer, then return the mime given\n    return fileTypeFromBuffer.mime;\n  }\n\n  if (path) {\n    // If a path is supplied, and fileType yields no results, then retry with MIME\n    // Path can be either a file path or a url\n    return MIME.getType(path);\n  }\n\n  return null;\n}\n\n/*\n * Obtains image orientation from EXIF metadata.\n *\n * @param img {Jimp} a Jimp image object\n * @returns {number} a number 1-8 representing EXIF orientation,\n *          in particular 1 if orientation tag is missing\n */\nfunction getExifOrientation(img) {\n  return (img._exif && img._exif.tags && img._exif.tags.Orientation) || 1;\n}\n\n/**\n * Returns a function which translates EXIF-rotated coordinates into\n * non-rotated ones.\n *\n * Transformation reference: http://sylvana.net/jpegcrop/exif_orientation.html.\n *\n * @param img {Jimp} a Jimp image object\n * @returns {function} transformation function for transformBitmap().\n */\nfunction getExifOrientationTransformation(img) {\n  const w = img.getWidth();\n  const h = img.getHeight();\n\n  switch (getExifOrientation(img)) {\n    case 1: // Horizontal (normal)\n      // does not need to be supported here\n      return null;\n\n    case 2: // Mirror horizontal\n      return function (x, y) {\n        return [w - x - 1, y];\n      };\n\n    case 3: // Rotate 180\n      return function (x, y) {\n        return [w - x - 1, h - y - 1];\n      };\n\n    case 4: // Mirror vertical\n      return function (x, y) {\n        return [x, h - y - 1];\n      };\n\n    case 5: // Mirror horizontal and rotate 270 CW\n      return function (x, y) {\n        return [y, x];\n      };\n\n    case 6: // Rotate 90 CW\n      return function (x, y) {\n        return [y, h - x - 1];\n      };\n\n    case 7: // Mirror horizontal and rotate 90 CW\n      return function (x, y) {\n        return [w - y - 1, h - x - 1];\n      };\n\n    case 8: // Rotate 270 CW\n      return function (x, y) {\n        return [w - y - 1, x];\n      };\n\n    default:\n      return null;\n  }\n}\n\n/*\n * Transforms bitmap in place (moves pixels around) according to given\n * transformation function.\n *\n * @param img {Jimp} a Jimp image object, which bitmap is supposed to\n *        be transformed\n * @param width {number} bitmap width after the transformation\n * @param height {number} bitmap height after the transformation\n * @param transformation {function} transformation function which defines pixel\n *        mapping between new and source bitmap. It takes a pair of coordinates\n *        in the target, and returns a respective pair of coordinates in\n *        the source bitmap, i.e. has following form:\n *        `function(new_x, new_y) { return [src_x, src_y] }`.\n */\nfunction transformBitmap(img, width, height, transformation) {\n  // Underscore-prefixed values are related to the source bitmap\n  // Their counterparts with no prefix are related to the target bitmap\n  const _data = img.bitmap.data;\n  const _width = img.bitmap.width;\n\n  const data = Buffer.alloc(_data.length);\n\n  for (let x = 0; x < width; x++) {\n    for (let y = 0; y < height; y++) {\n      const [_x, _y] = transformation(x, y);\n\n      const idx = (width * y + x) << 2;\n      const _idx = (_width * _y + _x) << 2;\n\n      const pixel = _data.readUInt32BE(_idx);\n      data.writeUInt32BE(pixel, idx);\n    }\n  }\n\n  img.bitmap.data = data;\n  img.bitmap.width = width;\n  img.bitmap.height = height;\n}\n\n/*\n * Automagically rotates an image based on its EXIF data (if present).\n * @param img {Jimp} a Jimp image object\n */\nfunction exifRotate(img) {\n  if (getExifOrientation(img) < 2) return;\n\n  const transformation = getExifOrientationTransformation(img);\n  const swapDimensions = getExifOrientation(img) > 4;\n\n  const newWidth = swapDimensions ? img.bitmap.height : img.bitmap.width;\n  const newHeight = swapDimensions ? img.bitmap.width : img.bitmap.height;\n\n  transformBitmap(img, newWidth, newHeight, transformation);\n}\n\n// parses a bitmap from the constructor to the JIMP bitmap property\nexport async function parseBitmap(data, path, cb) {\n  const mime = await getMIMEFromBuffer(data, path);\n\n  if (typeof mime !== \"string\") {\n    return cb(new Error(\"Could not find MIME for Buffer <\" + path + \">\"));\n  }\n\n  this._originalMime = mime.toLowerCase();\n\n  try {\n    const mime = this.getMIME();\n\n    if (this.constructor.decoders[mime]) {\n      this.bitmap = this.constructor.decoders[mime](data);\n    } else {\n      return throwError.call(this, \"Unsupported MIME type: \" + mime, cb);\n    }\n  } catch (error) {\n    return cb.call(this, error, this);\n  }\n\n  try {\n    this._exif = EXIFParser.create(data).parse();\n    exifRotate(this); // EXIF data\n  } catch (error) {\n    /* meh */\n  }\n\n  cb.call(this, null, this);\n\n  return this;\n}\n\nfunction compositeBitmapOverBackground(Jimp, image) {\n  return new Jimp(\n    image.bitmap.width,\n    image.bitmap.height,\n    image._background\n  ).composite(image, 0, 0).bitmap;\n}\n\n/**\n * Converts the image to a buffer\n * @param {(string|number)} mime the mime type of the image buffer to be created\n * @param {function(Error, Jimp)} cb a Node-style function to call with the buffer as the second argument\n * @returns {Jimp} this for chaining of methods\n */\nexport function getBuffer(mime, cb) {\n  if (mime === constants.AUTO) {\n    // allow auto MIME detection\n    mime = this.getMIME();\n  }\n\n  if (typeof mime !== \"string\") {\n    return throwError.call(this, \"mime must be a string\", cb);\n  }\n\n  if (typeof cb !== \"function\") {\n    return throwError.call(this, \"cb must be a function\", cb);\n  }\n\n  mime = mime.toLowerCase();\n\n  if (this._rgba && this.constructor.hasAlpha[mime]) {\n    this.bitmap.data = Buffer.from(this.bitmap.data);\n  } else {\n    // when format doesn't support alpha\n    // composite onto a new image so that the background shows through alpha channels\n    this.bitmap.data = compositeBitmapOverBackground(\n      this.constructor,\n      this\n    ).data;\n  }\n\n  if (this.constructor.encoders[mime]) {\n    const buffer = this.constructor.encoders[mime](this);\n    // Typically, buffers return a string or map.  However, the gif library \"gifwrap\" seemingly returns promises.\n    if (buffer instanceof Promise) {\n      // trigger the callback when the promise has been resolved\n      buffer.then((buff) => {\n        cb.call(this, null, buff);\n      });\n    } else {\n      cb.call(this, null, buffer);\n    }\n  } else {\n    return throwError.call(this, \"Unsupported MIME type: \" + mime, cb);\n  }\n\n  return this;\n}\n\nexport function getBufferAsync(mime) {\n  return promisify(getBuffer, this, mime);\n}\n"], "mappings": ";;;;;;;;AAAA;AAEA;AACA;AAEA;AACA;AACA;AAAoC;AAAA;AAAA;AAEpC,eAAeA,iBAAiB,CAACC,MAAM,EAAEC,IAAI,EAAE;EAC7C,MAAMC,kBAAkB,GAAG,MAAMC,iBAAQ,CAACC,UAAU,CAACJ,MAAM,CAAC;EAE5D,IAAIE,kBAAkB,EAAE;IACtB;IACA,OAAOA,kBAAkB,CAACG,IAAI;EAChC;EAEA,IAAIJ,IAAI,EAAE;IACR;IACA;IACA,OAAOK,IAAI,CAACC,OAAO,CAACN,IAAI,CAAC;EAC3B;EAEA,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,kBAAkB,CAACC,GAAG,EAAE;EAC/B,OAAQA,GAAG,CAACC,KAAK,IAAID,GAAG,CAACC,KAAK,CAACC,IAAI,IAAIF,GAAG,CAACC,KAAK,CAACC,IAAI,CAACC,WAAW,IAAK,CAAC;AACzE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gCAAgC,CAACJ,GAAG,EAAE;EAC7C,MAAMK,CAAC,GAAGL,GAAG,CAACM,QAAQ,EAAE;EACxB,MAAMC,CAAC,GAAGP,GAAG,CAACQ,SAAS,EAAE;EAEzB,QAAQT,kBAAkB,CAACC,GAAG,CAAC;IAC7B,KAAK,CAAC;MAAE;MACN;MACA,OAAO,IAAI;IAEb,KAAK,CAAC;MAAE;MACN,OAAO,UAAUS,CAAC,EAAEC,CAAC,EAAE;QACrB,OAAO,CAACL,CAAC,GAAGI,CAAC,GAAG,CAAC,EAAEC,CAAC,CAAC;MACvB,CAAC;IAEH,KAAK,CAAC;MAAE;MACN,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;QACrB,OAAO,CAACL,CAAC,GAAGI,CAAC,GAAG,CAAC,EAAEF,CAAC,GAAGG,CAAC,GAAG,CAAC,CAAC;MAC/B,CAAC;IAEH,KAAK,CAAC;MAAE;MACN,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;QACrB,OAAO,CAACD,CAAC,EAAEF,CAAC,GAAGG,CAAC,GAAG,CAAC,CAAC;MACvB,CAAC;IAEH,KAAK,CAAC;MAAE;MACN,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;QACrB,OAAO,CAACA,CAAC,EAAED,CAAC,CAAC;MACf,CAAC;IAEH,KAAK,CAAC;MAAE;MACN,OAAO,UAAUA,CAAC,EAAEC,CAAC,EAAE;QACrB,OAAO,CAACA,CAAC,EAAEH,CAAC,GAAGE,CAAC,GAAG,CAAC,CAAC;MACvB,CAAC;IAEH,KAAK,CAAC;MAAE;MACN,OAAO,UAAUA,CAAC,EAAEC,CAAC,EAAE;QACrB,OAAO,CAACL,CAAC,GAAGK,CAAC,GAAG,CAAC,EAAEH,CAAC,GAAGE,CAAC,GAAG,CAAC,CAAC;MAC/B,CAAC;IAEH,KAAK,CAAC;MAAE;MACN,OAAO,UAAUA,CAAC,EAAEC,CAAC,EAAE;QACrB,OAAO,CAACL,CAAC,GAAGK,CAAC,GAAG,CAAC,EAAED,CAAC,CAAC;MACvB,CAAC;IAEH;MACE,OAAO,IAAI;EAAC;AAElB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,eAAe,CAACX,GAAG,EAAEY,KAAK,EAAEC,MAAM,EAAEC,cAAc,EAAE;EAC3D;EACA;EACA,MAAMC,KAAK,GAAGf,GAAG,CAACgB,MAAM,CAACC,IAAI;EAC7B,MAAMC,MAAM,GAAGlB,GAAG,CAACgB,MAAM,CAACJ,KAAK;EAE/B,MAAMK,IAAI,GAAGE,MAAM,CAACC,KAAK,CAACL,KAAK,CAACM,MAAM,CAAC;EAEvC,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,KAAK,EAAEH,CAAC,EAAE,EAAE;IAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,MAAM,EAAEH,CAAC,EAAE,EAAE;MAC/B,MAAM,CAACY,EAAE,EAAEC,EAAE,CAAC,GAAGT,cAAc,CAACL,CAAC,EAAEC,CAAC,CAAC;MAErC,MAAMc,GAAG,GAAIZ,KAAK,GAAGF,CAAC,GAAGD,CAAC,IAAK,CAAC;MAChC,MAAMgB,IAAI,GAAIP,MAAM,GAAGK,EAAE,GAAGD,EAAE,IAAK,CAAC;MAEpC,MAAMI,KAAK,GAAGX,KAAK,CAACY,YAAY,CAACF,IAAI,CAAC;MACtCR,IAAI,CAACW,aAAa,CAACF,KAAK,EAAEF,GAAG,CAAC;IAChC;EACF;EAEAxB,GAAG,CAACgB,MAAM,CAACC,IAAI,GAAGA,IAAI;EACtBjB,GAAG,CAACgB,MAAM,CAACJ,KAAK,GAAGA,KAAK;EACxBZ,GAAG,CAACgB,MAAM,CAACH,MAAM,GAAGA,MAAM;AAC5B;;AAEA;AACA;AACA;AACA;AACA,SAASgB,UAAU,CAAC7B,GAAG,EAAE;EACvB,IAAID,kBAAkB,CAACC,GAAG,CAAC,GAAG,CAAC,EAAE;EAEjC,MAAMc,cAAc,GAAGV,gCAAgC,CAACJ,GAAG,CAAC;EAC5D,MAAM8B,cAAc,GAAG/B,kBAAkB,CAACC,GAAG,CAAC,GAAG,CAAC;EAElD,MAAM+B,QAAQ,GAAGD,cAAc,GAAG9B,GAAG,CAACgB,MAAM,CAACH,MAAM,GAAGb,GAAG,CAACgB,MAAM,CAACJ,KAAK;EACtE,MAAMoB,SAAS,GAAGF,cAAc,GAAG9B,GAAG,CAACgB,MAAM,CAACJ,KAAK,GAAGZ,GAAG,CAACgB,MAAM,CAACH,MAAM;EAEvEF,eAAe,CAACX,GAAG,EAAE+B,QAAQ,EAAEC,SAAS,EAAElB,cAAc,CAAC;AAC3D;;AAEA;AACO,eAAemB,WAAW,CAAChB,IAAI,EAAEzB,IAAI,EAAE0C,EAAE,EAAE;EAChD,MAAMtC,IAAI,GAAG,MAAMN,iBAAiB,CAAC2B,IAAI,EAAEzB,IAAI,CAAC;EAEhD,IAAI,OAAOI,IAAI,KAAK,QAAQ,EAAE;IAC5B,OAAOsC,EAAE,CAAC,IAAIC,KAAK,CAAC,kCAAkC,GAAG3C,IAAI,GAAG,GAAG,CAAC,CAAC;EACvE;EAEA,IAAI,CAAC4C,aAAa,GAAGxC,IAAI,CAACyC,WAAW,EAAE;EAEvC,IAAI;IACF,MAAMzC,IAAI,GAAG,IAAI,CAAC0C,OAAO,EAAE;IAE3B,IAAI,IAAI,CAACC,WAAW,CAACC,QAAQ,CAAC5C,IAAI,CAAC,EAAE;MACnC,IAAI,CAACoB,MAAM,GAAG,IAAI,CAACuB,WAAW,CAACC,QAAQ,CAAC5C,IAAI,CAAC,CAACqB,IAAI,CAAC;IACrD,CAAC,MAAM;MACL,OAAOwB,iBAAU,CAACC,IAAI,CAAC,IAAI,EAAE,yBAAyB,GAAG9C,IAAI,EAAEsC,EAAE,CAAC;IACpE;EACF,CAAC,CAAC,OAAOS,KAAK,EAAE;IACd,OAAOT,EAAE,CAACQ,IAAI,CAAC,IAAI,EAAEC,KAAK,EAAE,IAAI,CAAC;EACnC;EAEA,IAAI;IACF,IAAI,CAAC1C,KAAK,GAAG2C,mBAAU,CAACC,MAAM,CAAC5B,IAAI,CAAC,CAAC6B,KAAK,EAAE;IAC5CjB,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC,OAAOc,KAAK,EAAE;IACd;EAAA;EAGFT,EAAE,CAACQ,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEzB,OAAO,IAAI;AACb;AAEA,SAASK,6BAA6B,CAACC,IAAI,EAAEC,KAAK,EAAE;EAClD,OAAO,IAAID,IAAI,CACbC,KAAK,CAACjC,MAAM,CAACJ,KAAK,EAClBqC,KAAK,CAACjC,MAAM,CAACH,MAAM,EACnBoC,KAAK,CAACC,WAAW,CAClB,CAACC,SAAS,CAACF,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAACjC,MAAM;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASoC,SAAS,CAACxD,IAAI,EAAEsC,EAAE,EAAE;EAClC,IAAItC,IAAI,KAAKyD,SAAS,CAACC,IAAI,EAAE;IAC3B;IACA1D,IAAI,GAAG,IAAI,CAAC0C,OAAO,EAAE;EACvB;EAEA,IAAI,OAAO1C,IAAI,KAAK,QAAQ,EAAE;IAC5B,OAAO6C,iBAAU,CAACC,IAAI,CAAC,IAAI,EAAE,uBAAuB,EAAER,EAAE,CAAC;EAC3D;EAEA,IAAI,OAAOA,EAAE,KAAK,UAAU,EAAE;IAC5B,OAAOO,iBAAU,CAACC,IAAI,CAAC,IAAI,EAAE,uBAAuB,EAAER,EAAE,CAAC;EAC3D;EAEAtC,IAAI,GAAGA,IAAI,CAACyC,WAAW,EAAE;EAEzB,IAAI,IAAI,CAACkB,KAAK,IAAI,IAAI,CAAChB,WAAW,CAACiB,QAAQ,CAAC5D,IAAI,CAAC,EAAE;IACjD,IAAI,CAACoB,MAAM,CAACC,IAAI,GAAGE,MAAM,CAACsC,IAAI,CAAC,IAAI,CAACzC,MAAM,CAACC,IAAI,CAAC;EAClD,CAAC,MAAM;IACL;IACA;IACA,IAAI,CAACD,MAAM,CAACC,IAAI,GAAG8B,6BAA6B,CAC9C,IAAI,CAACR,WAAW,EAChB,IAAI,CACL,CAACtB,IAAI;EACR;EAEA,IAAI,IAAI,CAACsB,WAAW,CAACmB,QAAQ,CAAC9D,IAAI,CAAC,EAAE;IACnC,MAAML,MAAM,GAAG,IAAI,CAACgD,WAAW,CAACmB,QAAQ,CAAC9D,IAAI,CAAC,CAAC,IAAI,CAAC;IACpD;IACA,IAAIL,MAAM,YAAYoE,OAAO,EAAE;MAC7B;MACApE,MAAM,CAACqE,IAAI,CAAEC,IAAI,IAAK;QACpB3B,EAAE,CAACQ,IAAI,CAAC,IAAI,EAAE,IAAI,EAAEmB,IAAI,CAAC;MAC3B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL3B,EAAE,CAACQ,IAAI,CAAC,IAAI,EAAE,IAAI,EAAEnD,MAAM,CAAC;IAC7B;EACF,CAAC,MAAM;IACL,OAAOkD,iBAAU,CAACC,IAAI,CAAC,IAAI,EAAE,yBAAyB,GAAG9C,IAAI,EAAEsC,EAAE,CAAC;EACpE;EAEA,OAAO,IAAI;AACb;AAEO,SAAS4B,cAAc,CAAClE,IAAI,EAAE;EACnC,OAAO,IAAAmE,kBAAS,EAACX,SAAS,EAAE,IAAI,EAAExD,IAAI,CAAC;AACzC"}