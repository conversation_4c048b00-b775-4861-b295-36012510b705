{"name": "@jimp/plugin-cover", "version": "0.22.12", "description": "cover an image.", "main": "dist/index.js", "module": "es/index.js", "repository": "jimp-dev/jimp", "types": "index.d.ts", "author": "", "license": "MIT", "dependencies": {"@jimp/utils": "^0.22.12"}, "peerDependencies": {"@jimp/custom": ">=0.3.5", "@jimp/plugin-crop": ">=0.3.5", "@jimp/plugin-resize": ">=0.3.5", "@jimp/plugin-scale": ">=0.3.5"}, "devDependencies": {"@jimp/custom": "^0.22.12", "@jimp/plugin-crop": "^0.22.12", "@jimp/plugin-resize": "^0.22.12", "@jimp/plugin-scale": "^0.22.12", "@jimp/test-utils": "^0.22.12"}, "publishConfig": {"access": "public"}}