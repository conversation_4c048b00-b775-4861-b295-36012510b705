{"version": 3, "file": "index.js", "names": ["<PERSON><PERSON>", "addType", "addJimpMethods", "addConstants", "jimp<PERSON>v<PERSON><PERSON><PERSON>", "configure", "configuration", "jimpInstance", "jimpConfig", "has<PERSON><PERSON><PERSON>", "encoders", "decoders", "class", "constants", "addToConfig", "newConfig", "Object", "entries", "for<PERSON>ach", "key", "value", "addImageType", "typeModule", "type", "Array", "isArray", "mime", "mimeType", "addPlugin", "pluginModule", "plugin", "types", "plugins"], "sources": ["../src/index.js"], "sourcesContent": ["import Jimp, {\n  addType,\n  addJimpMethods,\n  addConstants,\n  jimpEvChange,\n} from \"@jimp/core\";\n\nexport default function configure(configuration, jimpInstance = Jimp) {\n  const jimpConfig = {\n    hasAlpha: {},\n    encoders: {},\n    decoders: {},\n    class: {},\n    constants: {},\n  };\n\n  function addToConfig(newConfig) {\n    Object.entries(newConfig).forEach(([key, value]) => {\n      jimpConfig[key] = {\n        ...jimpConfig[key],\n        ...value,\n      };\n    });\n  }\n\n  function addImageType(typeModule) {\n    const type = typeModule();\n\n    if (Array.isArray(type.mime)) {\n      addType(...type.mime);\n    } else {\n      Object.entries(type.mime).forEach((mimeType) => addType(...mimeType));\n    }\n\n    delete type.mime;\n    addToConfig(type);\n  }\n\n  function addPlugin(pluginModule) {\n    const plugin = pluginModule(jimpEvChange) || {};\n    if (!plugin.class && !plugin.constants) {\n      // Default to class function\n      addToConfig({ class: plugin });\n    } else {\n      addToConfig(plugin);\n    }\n  }\n\n  if (configuration.types) {\n    configuration.types.forEach(addImageType);\n\n    jimpInstance.decoders = {\n      ...jimpInstance.decoders,\n      ...jimpConfig.decoders,\n    };\n    jimpInstance.encoders = {\n      ...jimpInstance.encoders,\n      ...jimpConfig.encoders,\n    };\n    jimpInstance.hasAlpha = {\n      ...jimpInstance.hasAlpha,\n      ...jimpConfig.hasAlpha,\n    };\n  }\n\n  if (configuration.plugins) {\n    configuration.plugins.forEach(addPlugin);\n  }\n\n  addJimpMethods(jimpConfig.class, jimpInstance);\n  addConstants(jimpConfig.constants, jimpInstance);\n\n  return Jimp;\n}\n"], "mappings": "AAAA,OAAOA,IAAI,IACTC,OAAO,EACPC,cAAc,EACdC,YAAY,EACZC,YAAY,QACP,YAAY;AAEnB,eAAe,SAASC,SAAS,CAACC,aAAa,EAAuB;EAAA,IAArBC,YAAY,uEAAGP,IAAI;EAClE,MAAMQ,UAAU,GAAG;IACjBC,QAAQ,EAAE,CAAC,CAAC;IACZC,QAAQ,EAAE,CAAC,CAAC;IACZC,QAAQ,EAAE,CAAC,CAAC;IACZC,KAAK,EAAE,CAAC,CAAC;IACTC,SAAS,EAAE,CAAC;EACd,CAAC;EAED,SAASC,WAAW,CAACC,SAAS,EAAE;IAC9BC,MAAM,CAACC,OAAO,CAACF,SAAS,CAAC,CAACG,OAAO,CAAC,QAAkB;MAAA,IAAjB,CAACC,GAAG,EAAEC,KAAK,CAAC;MAC7CZ,UAAU,CAACW,GAAG,CAAC,GAAG;QAChB,GAAGX,UAAU,CAACW,GAAG,CAAC;QAClB,GAAGC;MACL,CAAC;IACH,CAAC,CAAC;EACJ;EAEA,SAASC,YAAY,CAACC,UAAU,EAAE;IAChC,MAAMC,IAAI,GAAGD,UAAU,EAAE;IAEzB,IAAIE,KAAK,CAACC,OAAO,CAACF,IAAI,CAACG,IAAI,CAAC,EAAE;MAC5BzB,OAAO,CAAC,GAAGsB,IAAI,CAACG,IAAI,CAAC;IACvB,CAAC,MAAM;MACLV,MAAM,CAACC,OAAO,CAACM,IAAI,CAACG,IAAI,CAAC,CAACR,OAAO,CAAES,QAAQ,IAAK1B,OAAO,CAAC,GAAG0B,QAAQ,CAAC,CAAC;IACvE;IAEA,OAAOJ,IAAI,CAACG,IAAI;IAChBZ,WAAW,CAACS,IAAI,CAAC;EACnB;EAEA,SAASK,SAAS,CAACC,YAAY,EAAE;IAC/B,MAAMC,MAAM,GAAGD,YAAY,CAACzB,YAAY,CAAC,IAAI,CAAC,CAAC;IAC/C,IAAI,CAAC0B,MAAM,CAAClB,KAAK,IAAI,CAACkB,MAAM,CAACjB,SAAS,EAAE;MACtC;MACAC,WAAW,CAAC;QAAEF,KAAK,EAAEkB;MAAO,CAAC,CAAC;IAChC,CAAC,MAAM;MACLhB,WAAW,CAACgB,MAAM,CAAC;IACrB;EACF;EAEA,IAAIxB,aAAa,CAACyB,KAAK,EAAE;IACvBzB,aAAa,CAACyB,KAAK,CAACb,OAAO,CAACG,YAAY,CAAC;IAEzCd,YAAY,CAACI,QAAQ,GAAG;MACtB,GAAGJ,YAAY,CAACI,QAAQ;MACxB,GAAGH,UAAU,CAACG;IAChB,CAAC;IACDJ,YAAY,CAACG,QAAQ,GAAG;MACtB,GAAGH,YAAY,CAACG,QAAQ;MACxB,GAAGF,UAAU,CAACE;IAChB,CAAC;IACDH,YAAY,CAACE,QAAQ,GAAG;MACtB,GAAGF,YAAY,CAACE,QAAQ;MACxB,GAAGD,UAAU,CAACC;IAChB,CAAC;EACH;EAEA,IAAIH,aAAa,CAAC0B,OAAO,EAAE;IACzB1B,aAAa,CAAC0B,OAAO,CAACd,OAAO,CAACU,SAAS,CAAC;EAC1C;EAEA1B,cAAc,CAACM,UAAU,CAACI,KAAK,EAAEL,YAAY,CAAC;EAC9CJ,YAAY,CAACK,UAAU,CAACK,SAAS,EAAEN,YAAY,CAAC;EAEhD,OAAOP,IAAI;AACb"}