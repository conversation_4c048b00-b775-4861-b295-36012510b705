{"name": "@jimp/core", "version": "0.22.12", "description": "<PERSON><PERSON> core", "main": "dist/index.js", "module": "es/index.js", "types": "types/index.d.ts", "sideEffects": false, "files": ["dist", "es", "index.d.ts", "fonts", "types"], "repository": "jimp-dev/jimp", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"@jimp/utils": "^0.22.12", "any-base": "^1.1.0", "buffer": "^5.2.0", "exif-parser": "^0.1.12", "file-type": "^16.5.4", "isomorphic-fetch": "^3.0.0", "pixelmatch": "^4.0.2", "tinycolor2": "^1.6.0"}, "publishConfig": {"access": "public"}}